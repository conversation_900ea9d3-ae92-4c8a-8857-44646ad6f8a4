<template>
  <div class="game-management">
    <div class="page-header">
      <h2>游戏管理</h2>
      <p>管理系统支持的游戏类型和段位配置</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        添加游戏
      </el-button>
      <el-button @click="handleRefresh">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 游戏列表 -->
    <el-card>
      <el-table 
        :data="gameStore.games" 
        :loading="gameStore.gamesLoading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="displayName" label="游戏名称" min-width="150">
          <template #default="{ row }">
            <div class="game-info">
              <img v-if="row.icon" :src="row.icon" class="game-icon" />
              <div>
                <div class="game-name">{{ row.displayName }}</div>
                <div class="game-code">{{ row.name }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        
        <el-table-column label="段位数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="info">{{ row.ranks?.length || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="订单数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="success">{{ row._count?.orders || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="员工技能" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="warning">{{ row._count?.employeeSkills || 0 }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="表单字段" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="primary">{{ row._count?.formFields || 0 }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="sortOrder" label="排序" width="80" align="center" />
        
        <el-table-column label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.isActive"
              @change="handleToggleStatus(row)"
              :loading="loading"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="300" align="center" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleConfigureFormFields(row)" type="success">
              配置表单
            </el-button>
            <el-button size="small" @click="handleViewRanks(row)">
              段位管理
            </el-button>
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(row)"
              :disabled="row._count?.orders > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="gameStore.gamesPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑游戏对话框 -->
    <GameEditDialog
      v-model="showCreateDialog"
      :game-id="editingGameId"
      @success="handleDialogSuccess"
    />

    <!-- 段位管理对话框 -->
    <GameRankDialog
      v-model="showRankDialog"
      :game-id="selectedGameId"
      :game-name="selectedGameName"
    />

    <!-- 表单字段配置对话框 -->
    <FormFieldConfigDialog
      v-model="showFormFieldDialog"
      :game-id="selectedGameId"
      :game-name="selectedGameName"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Refresh } from '@element-plus/icons-vue';
import { useGameStore } from '@/stores/game';
import GameEditDialog from '@/components/GameEditDialog.vue';
import GameRankDialog from '@/components/GameRankDialog.vue';
import FormFieldConfigDialog from '@/components/FormFieldConfigDialog.vue';
import type { Game } from '@/types/game';

const gameStore = useGameStore();

// 响应式数据
const loading = ref(false);
const showCreateDialog = ref(false);
const showRankDialog = ref(false);
const showFormFieldDialog = ref(false);
const editingGameId = ref<string>('');
const selectedGameId = ref<string>('');
const selectedGameName = ref<string>('');

// 分页参数
const currentPage = ref(1);
const pageSize = ref(10);

// 方法
const fetchGames = async () => {
  try {
    await gameStore.fetchGames({
      page: currentPage.value,
      limit: pageSize.value,
      sortBy: 'sortOrder',
      sortOrder: 'asc'
    });
  } catch (error) {
    console.error('获取游戏列表失败:', error);
    ElMessage.error('获取游戏列表失败');
  }
};

const handleRefresh = () => {
  fetchGames();
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchGames();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  fetchGames();
};

const handleToggleStatus = async (game: Game) => {
  try {
    loading.value = true;
    await gameStore.updateGame(game.id, { isActive: game.isActive });
    ElMessage.success(`游戏已${game.isActive ? '启用' : '禁用'}`);
  } catch (error) {
    console.error('更新游戏状态失败:', error);
    ElMessage.error('更新游戏状态失败');
    // 恢复原状态
    game.isActive = !game.isActive;
  } finally {
    loading.value = false;
  }
};

const handleEdit = (game: Game) => {
  editingGameId.value = game.id;
  showCreateDialog.value = true;
};

const handleDelete = async (game: Game) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除游戏"${game.displayName}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    loading.value = true;
    await gameStore.deleteGame(game.id);
    ElMessage.success('游戏删除成功');
    await fetchGames();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除游戏失败:', error);
      ElMessage.error('删除游戏失败');
    }
  } finally {
    loading.value = false;
  }
};

const handleViewRanks = (game: Game) => {
  selectedGameId.value = game.id;
  selectedGameName.value = game.displayName;
  showRankDialog.value = true;
};

const handleConfigureFormFields = (game: Game) => {
  selectedGameId.value = game.id;
  selectedGameName.value = game.displayName;
  showFormFieldDialog.value = true;
};

const handleDialogSuccess = () => {
  showCreateDialog.value = false;
  showFormFieldDialog.value = false;
  editingGameId.value = '';
  fetchGames();
};

// 生命周期
onMounted(() => {
  fetchGames();
});
</script>

<style lang="scss" scoped>
.game-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: var(--el-text-color-primary);
  }

  p {
    margin: 0;
    color: var(--el-text-color-regular);
  }
}

.action-bar {
  margin-bottom: 20px;
  display: flex;
  gap: 12px;
}

.game-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.game-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 4px;
}

.game-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.game-code {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
