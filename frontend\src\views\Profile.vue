<template>
  <div class="profile-container">
    <!-- 简化的页面头部 -->
    <div class="profile-header">
      <div class="header-background"></div>
      <div class="header-content">
        <div class="breadcrumb-section">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>
              <el-button
                type="text"
                size="small"
                @click="goToHomePage"
                class="breadcrumb-btn"
              >
                <el-icon><HomeFilled /></el-icon>
                {{ getHomePageName() }}
              </el-button>
            </el-breadcrumb-item>
            <el-breadcrumb-item>个人中心</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 用户信息展示区域 -->
        <div class="user-info-section">
          <div class="avatar-wrapper">
            <el-avatar
              :size="80"
              class="user-avatar"
              :src="userInfo?.avatar"
            >
              {{ getUserInitial() }}
            </el-avatar>
            <div class="avatar-badge" v-if="userInfo">
              <el-tag :type="getRoleType(userInfo.role)" size="small" effect="dark">
                {{ getRoleText(userInfo.role) }}
              </el-tag>
            </div>
          </div>
          <div class="user-basic-info">
            <h1 class="user-name">{{ userInfo?.nickname || userInfo?.username || '用户' }}</h1>
            <p class="user-desc">{{ getUserDescription() }}</p>
            <div class="user-status">
              <el-tag
                v-if="userInfo"
                :type="getStatusType(userInfo.status)"
                size="small"
                effect="light"
              >
                {{ getStatusText(userInfo.status) }}
              </el-tag>
              <span class="join-time">{{ formatJoinTime() }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="profile-content" v-loading="loading">
      <!-- 基本信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <el-icon class="header-icon"><User /></el-icon>
              <span>基本信息</span>
            </div>
          </div>
        </template>

        <div class="info-content" v-if="userInfo">
          <div class="info-grid">
            <div class="info-row">
              <div class="info-item">
                <div class="info-label">
                  <el-icon><User /></el-icon>
                  用户名
                </div>
                <div class="info-value">{{ userInfo.username }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">
                  <el-icon><Star /></el-icon>
                  昵称
                </div>
                <div class="info-value">{{ userInfo.nickname || '未设置' }}</div>
              </div>
            </div>

            <div class="info-row" v-if="userInfo.phone || userInfo.level">
              <div class="info-item" v-if="userInfo.phone">
                <div class="info-label">
                  <el-icon><Phone /></el-icon>
                  手机号
                </div>
                <div class="info-value">{{ userInfo.phone }}</div>
              </div>
              <div class="info-item" v-if="userInfo.level">
                <div class="info-label">
                  <el-icon><Trophy /></el-icon>
                  等级
                </div>
                <div class="info-value">
                  <el-tag type="warning" size="small">Lv.{{ userInfo.level }}</el-tag>
                </div>
              </div>
            </div>

            <div class="info-row">
              <div class="info-item">
                <div class="info-label">
                  <el-icon><Calendar /></el-icon>
                  注册时间
                </div>
                <div class="info-value">{{ formatDate(userInfo.createdAt) }}</div>
              </div>
              <div class="info-item">
                <div class="info-label">
                  <el-icon><Clock /></el-icon>
                  最后登录
                </div>
                <div class="info-value">{{ formatDate(userInfo.lastLoginAt) }}</div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-data">
          <el-empty description="暂无用户信息" />
        </div>
      </el-card>

      <!-- 核心数据概览 -->
      <el-card v-if="userStats" class="stats-overview" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <el-icon class="header-icon"><DataAnalysis /></el-icon>
              <span>数据概览</span>
            </div>
            <el-button
              size="small"
              type="text"
              @click="refreshStats"
              :loading="statsLoading"
              class="refresh-btn"
            >
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </template>

        <div class="stats-content">
          <!-- 简化的统计展示 -->
          <div class="core-stats">
            <template v-if="userInfo?.role === 'EMPLOYEE'">
              <div class="stat-item primary">
                <div class="stat-icon">
                  <el-icon><Calendar /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ userStats.currentMonthTasks || 0 }}</div>
                  <div class="stat-label">本月任务</div>
                </div>
              </div>
              <div class="stat-item success">
                <div class="stat-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">¥{{ userStats.currentMonthEarnings || 0 }}</div>
                  <div class="stat-label">本月收益</div>
                </div>
              </div>
            </template>

            <template v-else>
              <div class="stat-item primary">
                <div class="stat-icon">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ userStats.todayOrders || 0 }}</div>
                  <div class="stat-label">今日订单</div>
                </div>
              </div>
              <div class="stat-item success">
                <div class="stat-icon">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">¥{{ userStats.weekRevenue || 0 }}</div>
                  <div class="stat-label">本周营收</div>
                </div>
              </div>
            </template>
          </div>

          <!-- 查看详细统计链接 -->
          <div class="stats-action">
            <el-button type="text" @click="goToDetailStats" class="detail-link">
              查看详细统计
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 账户管理区域 -->
      <el-card class="account-management" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <el-icon class="header-icon"><Operation /></el-icon>
              <span>账户管理</span>
            </div>
          </div>
        </template>

        <div class="management-content">
          <div class="management-section">
            <div class="section-title">
              <el-icon><User /></el-icon>
              <span>个人信息</span>
            </div>
            <el-button type="primary" @click="openEditDialog" :icon="Edit">
              编辑资料
            </el-button>
          </div>

          <div class="management-section">
            <div class="section-title">
              <el-icon><Lock /></el-icon>
              <span>账户安全</span>
            </div>
            <el-button type="info" @click="showPasswordDialog = true" :icon="Key">
              修改密码
            </el-button>
          </div>

          <div class="management-section">
            <div class="section-title">
              <el-icon><SwitchButton /></el-icon>
              <span>账户操作</span>
            </div>
            <el-button
              type="warning"
              @click="handleLogout"
              :loading="logoutLoading"
              :icon="SwitchButton"
            >
              退出登录
            </el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 编辑用户信息对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑个人信息"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="editForm.phone" placeholder="请输入手机号" />
        </el-form-item>

      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showEditDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateProfile" :loading="updateLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showPasswordDialog = false">取消</el-button>
          <el-button type="primary" @click="handleUpdatePassword" :loading="passwordLoading">
            修改密码
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Edit,
  Refresh,
  SwitchButton,
  ArrowLeft,
  ArrowRight,
  HomeFilled,
  User,
  Star,
  Phone,
  Trophy,
  Calendar,
  Clock,
  Lock,
  Key,
  DataAnalysis,
  Money,
  Document,
  Operation
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { authApi } from '@/api/auth'
import { userApi } from '@/api/users'

const router = useRouter()
const authStore = useAuthStore()

// 图标引用
const ArrowLeftIcon = ArrowLeft
const HomeIcon = HomeFilled

// 响应式数据
const loading = ref(false)
const statsLoading = ref(false)
const updateLoading = ref(false)
const passwordLoading = ref(false)
const logoutLoading = ref(false)

const userInfo = ref<any>(null)
const userStats = ref<any>(null)

// 对话框控制
const showEditDialog = ref(false)
const showPasswordDialog = ref(false)

// 表单引用
const editFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

// 编辑表单
const editForm = reactive({
  nickname: '',
  phone: ''
})

// 密码表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const editRules: FormRules = {
  nickname: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const passwordRules: FormRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

onMounted(() => {
  loadUserInfo()
  loadUserStats()
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    loading.value = true
    const response = await authApi.getCurrentUser()
    if (response.success) {
      userInfo.value = response.data
      // 更新store中的用户信息
      authStore.updateUserInfo(response.data)
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    ElMessage.error('获取用户信息失败')
  } finally {
    loading.value = false
  }
}

// 加载用户统计信息
const loadUserStats = async () => {
  try {
    statsLoading.value = true
    const response = await userApi.getMyStats()
    if (response.success) {
      userStats.value = response.data
    }
  } catch (error: any) {
    console.error('获取统计信息失败:', error)

    // 根据错误类型提供不同的提示
    if (error.status === 404) {
      ElMessage.warning('暂无统计数据')
    } else if (error.status === 403) {
      ElMessage.error('没有权限查看统计信息')
    } else {
      ElMessage.error('获取统计信息失败，请稍后重试')
    }
  } finally {
    statsLoading.value = false
  }
}

// 刷新统计信息
const refreshStats = () => {
  loadUserStats()
}

// 获取用户头像首字母
const getUserInitial = () => {
  if (!userInfo.value) return 'U'
  const name = userInfo.value.nickname || userInfo.value.username
  return name.charAt(0).toUpperCase()
}

// 获取用户描述
const getUserDescription = () => {
  if (!userInfo.value) return '欢迎使用系统'

  const roleTexts = {
    BOSS: '系统管理员，拥有最高权限',
    ADMIN: '管理员，负责日常运营管理',
    EMPLOYEE: '员工，负责任务执行'
  }

  return roleTexts[userInfo.value.role] || '系统用户'
}

// 格式化加入时间
const formatJoinTime = () => {
  if (!userInfo.value?.createdAt) return ''
  const date = new Date(userInfo.value.createdAt)
  const now = new Date()
  const diffTime = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 30) {
    return `加入 ${diffDays} 天`
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30)
    return `加入 ${months} 个月`
  } else {
    const years = Math.floor(diffDays / 365)
    return `加入 ${years} 年`
  }
}

// 处理编辑个人信息
const handleUpdateProfile = async () => {
  if (!editFormRef.value) return

  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return

    updateLoading.value = true
    const response = await userApi.updateUser(userInfo.value.id, editForm)

    if (response.success) {
      ElMessage.success('个人信息更新成功')
      showEditDialog.value = false
      await loadUserInfo() // 重新加载用户信息
    }
  } catch (error: any) {
    ElMessage.error(error.message || '更新失败')
  } finally {
    updateLoading.value = false
  }
}

// 处理修改密码
const handleUpdatePassword = async () => {
  if (!passwordFormRef.value) return

  try {
    const valid = await passwordFormRef.value.validate()
    if (!valid) return

    passwordLoading.value = true
    const response = await authApi.updatePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword
    })

    if (response.success) {
      ElMessage.success('密码修改成功')
      showPasswordDialog.value = false
      // 重置表单
      passwordForm.currentPassword = ''
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
    }
  } catch (error: any) {
    ElMessage.error(error.message || '密码修改失败')
  } finally {
    passwordLoading.value = false
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    logoutLoading.value = true
    await authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error) {
    // 用户取消操作
  } finally {
    logoutLoading.value = false
  }
}

// 打开编辑对话框时初始化表单
const openEditDialog = () => {
  if (userInfo.value) {
    editForm.nickname = userInfo.value.nickname || ''
    editForm.phone = userInfo.value.phone || ''
  }
  showEditDialog.value = true
}

// 工具方法
const getRoleType = (role: string) => {
  switch (role) {
    case 'ADMIN': return 'danger'
    case 'BOSS': return 'warning'
    case 'EMPLOYEE': return 'success'
    default: return 'info'
  }
}

const getRoleText = (role: string) => {
  switch (role) {
    case 'ADMIN': return '管理员'
    case 'BOSS': return '老板'
    case 'EMPLOYEE': return '员工'
    default: return '未知'
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'ACTIVE': return 'success'
    case 'INACTIVE': return 'warning'
    case 'BANNED': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'ACTIVE': return '正常'
    case 'INACTIVE': return '未激活'
    case 'BANNED': return '已禁用'
    default: return '未知'
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 导航相关方法
const goToHomePage = () => {
  const homePath = authStore.getRedirectPath()
  router.push(homePath)
}

const getHomePageName = () => {
  if (!authStore.user) return '首页'

  switch (authStore.user.role) {
    case 'BOSS':
    case 'ADMIN':
      return '订单管理'
    case 'EMPLOYEE':
      return '可接单任务'
    default:
      return '首页'
  }
}

// 跳转到详细统计页面
const goToDetailStats = () => {
  if (userInfo.value?.role === 'EMPLOYEE') {
    router.push('/employee/earnings')
  } else {
    router.push('/boss/statistics')
  }
}
</script>

<style lang="scss" scoped>
.profile-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

.profile-header {
  position: relative;
  background: white;
  border-radius: 0 0 24px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 32px;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 1;
  }

  .header-content {
    position: relative;
    z-index: 2;
    padding: 24px 32px 32px;

    .breadcrumb-section {
      margin-bottom: 24px;

      :deep(.el-breadcrumb) {
        .el-breadcrumb__item {
          .el-breadcrumb__inner {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
          }

          &:last-child .el-breadcrumb__inner {
            color: white;
            font-weight: 600;
          }
        }

        .el-breadcrumb__separator {
          color: rgba(255, 255, 255, 0.7);
        }
      }

      .breadcrumb-btn {
        color: rgba(255, 255, 255, 0.9);
        padding: 4px 8px;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: white;
        }

        .el-icon {
          margin-right: 4px;
        }
      }
    }

    .header-main {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      gap: 32px;

      .user-avatar-section {
        display: flex;
        align-items: center;
        gap: 20px;

        .avatar-wrapper {
          position: relative;

          .user-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 32px;
            font-weight: 600;
            border: 4px solid white;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
          }

          .avatar-badge {
            position: absolute;
            bottom: -4px;
            right: -4px;
            z-index: 3;
          }
        }

        .user-basic-info {
          .user-name {
            margin: 0 0 8px 0;
            font-size: 28px;
            font-weight: 700;
            color: white;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          .user-desc {
            margin: 0 0 12px 0;
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            line-height: 1.5;
          }

          .user-status {
            display: flex;
            align-items: center;
            gap: 16px;

            .join-time {
              color: rgba(255, 255, 255, 0.8);
              font-size: 14px;
            }
          }
        }
      }

      .header-actions {
        display: flex;
        gap: 12px;
        flex-shrink: 0;

        .edit-btn {
          background: rgba(255, 255, 255, 0.15);
          border: 1px solid rgba(255, 255, 255, 0.3);
          color: white;
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
          }
        }

        .back-btn {
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(10px);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
          }
        }
      }
    }
  }
}

// 主要内容区域
.profile-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 32px 32px;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @media (max-width: 768px) {
    padding: 0 16px 16px;
    gap: 16px;
  }
}

// 卡片样式
.info-card,
.stats-overview,
.account-management {
  border-radius: 16px;
  border: none;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  }

  :deep(.el-card__header) {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e2e8f0;
    border-radius: 16px 16px 0 0;
    padding: 20px 24px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 600;
        color: #1e293b;
        font-size: 16px;

        .header-icon {
          color: #3b82f6;
          font-size: 18px;
        }
      }

      .refresh-btn {
        color: #64748b;
        padding: 4px;

        &:hover {
          color: #3b82f6;
          background: rgba(59, 130, 246, 0.1);
        }
      }
    }
  }

  :deep(.el-card__body) {
    padding: 24px;
  }
}

// 信息内容样式
.info-content {
  .info-grid {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .info-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 24px;
    }

    .info-item {
      .info-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
        color: #64748b;
        margin-bottom: 8px;

        .el-icon {
          color: #3b82f6;
          font-size: 16px;
        }
      }

      .info-value {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        line-height: 1.5;
      }
    }
  }
}

// 安全设置样式
.security-content {
  .security-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #fef7ff 0%, #f3f4f6 100%);
    border-radius: 12px;
    border: 1px solid #e5e7eb;

    .security-info {
      .security-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 4px;

        .el-icon {
          color: #8b5cf6;
          font-size: 18px;
        }
      }

      .security-desc {
        font-size: 14px;
        color: #64748b;
        margin: 0;
      }
    }
  }
}

// 统计卡片样式
.stats-content {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;

    .stat-card {
      padding: 20px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      gap: 16px;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      &.primary {
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        border: 1px solid #93c5fd;

        .stat-icon {
          background: #3b82f6;
          color: white;
        }
      }

      &.success {
        background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
        border: 1px solid #86efac;

        .stat-icon {
          background: #10b981;
          color: white;
        }
      }

      &.warning {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        border: 1px solid #fcd34d;

        .stat-icon {
          background: #f59e0b;
          color: white;
        }
      }

      &.info {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 1px solid #7dd3fc;

        .stat-icon {
          background: #0ea5e9;
          color: white;
        }
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        flex-shrink: 0;
      }

      .stat-info {
        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1e293b;
          line-height: 1.2;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
        }
      }
    }
  }
}
// 操作卡片样式
.actions-content {
  .action-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .action-btn {
      width: 100%;
      height: 48px;
      border-radius: 12px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.3s ease;
      border: 1px solid transparent;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      &.el-button--primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        }
      }

      &.el-button--info {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        border: none;
        color: white;

        &:hover {
          background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
        }
      }

      &.el-button--warning {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        border: none;
        color: white;

        &:hover {
          background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        }
      }
    }
  }
}

// 对话框样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
}

// 响应式设计
@media (max-width: 1024px) {
  .profile-content {
    .content-grid {
      grid-template-columns: 1fr;
      gap: 24px;
    }
  }

  .profile-header {
    .header-content {
      .header-main {
        flex-direction: column;
        align-items: flex-start;
        gap: 24px;

        .header-actions {
          width: 100%;
          justify-content: flex-start;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .profile-content {
    padding: 0 16px 16px;

    .info-content {
      .info-grid {
        .info-row {
          grid-template-columns: 1fr;
          gap: 16px;
        }
      }
    }

    .stats-content {
      .stats-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }
  }

  .profile-header {
    .header-content {
      padding: 16px 20px 24px;

      .header-main {
        .user-avatar-section {
          flex-direction: column;
          text-align: center;
          gap: 16px;
        }

        .header-actions {
          flex-direction: column;
          width: 100%;

          .edit-btn,
          .back-btn {
            width: 100%;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .stats-content {
    .stats-grid {
      .stat-card {
        padding: 16px;
        gap: 12px;

        .stat-icon {
          width: 40px;
          height: 40px;
          font-size: 18px;
        }

        .stat-info {
          .stat-value {
            font-size: 20px;
          }
        }
      }
    }
  }
}

// 加载状态
:deep(.el-loading-mask) {
  border-radius: var(--border-radius-base);
}

// 卡片样式优化
:deep(.el-card) {
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-lighter);

  .el-card__header {
    background: var(--bg-color);
    border-bottom: 1px solid var(--border-lighter);
  }

  .el-card__body {
    padding: var(--spacing-lg);
  }
}

// 表单样式优化
:deep(.el-form-item) {
  margin-bottom: var(--spacing-lg);
}

:deep(.el-input) {
  .el-input__wrapper {
    border-radius: var(--border-radius-base);
  }
}

// 标签样式
:deep(.el-tag) {
  border-radius: var(--border-radius-base);
  font-weight: 500;
}

// 头像样式
:deep(.el-avatar) {
  border: 3px solid var(--border-lighter);
}

// 统计概览样式
.stats-overview {
  .stats-content {
    .core-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      margin-bottom: 20px;

      .stat-item {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        border-radius: 12px;
        padding: 20px;
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all 0.3s ease;
        border: 1px solid #e2e8f0;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
        }

        &.primary {
          background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
          border-color: #93c5fd;

          .stat-icon {
            background: #3b82f6;
          }
        }

        &.success {
          background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
          border-color: #86efac;

          .stat-icon {
            background: #10b981;
          }
        }

        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
          flex-shrink: 0;
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #1e293b;
            line-height: 1.2;
            margin-bottom: 4px;
          }

          .stat-label {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
          }
        }
      }
    }

    .stats-action {
      text-align: center;
      padding-top: 16px;
      border-top: 1px solid #e2e8f0;

      .detail-link {
        color: #3b82f6;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 4px;
        transition: all 0.3s ease;

        &:hover {
          color: #2563eb;
          transform: translateX(2px);
        }
      }
    }
  }
}

// 账户管理样式
.account-management {
  .management-content {
    .management-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid #e2e8f0;

      &:last-child {
        border-bottom: none;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: #1e293b;

        .el-icon {
          color: #64748b;
          font-size: 16px;
        }
      }
    }
  }
}
</style>
