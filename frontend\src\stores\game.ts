import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { gameApi, gameRankApi } from '@/api/game';
import type {
  Game,
  GameRank,
  SimpleGame,
  SimpleGameRank,
  CreateGameRequest,
  UpdateGameRequest,
  CreateGameRankRequest,
  UpdateGameRankRequest,
  GameQuery,
  GameRankQuery,
  GameStatistics,
  GameHotness,
  PriceCalculationResult
} from '@/types/game';
import type { PaginatedResponse } from '@/types/common';

export const useGameStore = defineStore('game', () => {
  // 状态
  const games = ref<Game[]>([]);
  const activeGames = ref<SimpleGame[]>([]);
  const currentGame = ref<Game | null>(null);
  const gameRanks = ref<GameRank[]>([]);
  const currentGameRanks = ref<SimpleGameRank[]>([]);
  const gameStatistics = ref<GameStatistics | null>(null);
  const gameHotness = ref<GameHotness[]>([]);
  const priceCalculation = ref<PriceCalculationResult | null>(null);
  
  // 加载状态
  const loading = ref(false);
  const gamesLoading = ref(false);
  const ranksLoading = ref(false);
  const statisticsLoading = ref(false);
  
  // 分页信息
  const gamesPagination = ref({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });
  
  const ranksPagination = ref({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  // 计算属性
  const gameOptions = computed(() => {
    // 确保activeGames.value是数组
    if (!Array.isArray(activeGames.value)) {
      return [];
    }
    return activeGames.value.map(game => ({
      value: game.id,
      label: game.displayName,
      icon: game.icon
    }));
  });

  const rankOptions = computed(() => {
    // 确保currentGameRanks.value是数组
    if (!Array.isArray(currentGameRanks.value)) {
      return [];
    }
    return currentGameRanks.value.map(rank => ({
      value: rank.id,
      label: rank.displayName,
      level: rank.level,
      icon: rank.icon
    }));
  });

  // 获取活跃游戏列表
  const fetchActiveGames = async () => {
    try {
      loading.value = true;
      const response = await gameApi.getActiveGames();
      // 从API响应中提取data字段，确保是数组
      const gameList = Array.isArray(response?.data) ? response.data : [];
      activeGames.value = gameList;
      return gameList;
    } catch (error) {
      console.error('获取活跃游戏列表失败:', error);
      // 发生错误时设置为空数组，避免map函数报错
      activeGames.value = [];
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取游戏列表
  const fetchGames = async (query?: GameQuery) => {
    try {
      gamesLoading.value = true;
      const response = await gameApi.getGames(query);
      games.value = response.items;
      gamesPagination.value = response.pagination;
      return response;
    } catch (error) {
      console.error('获取游戏列表失败:', error);
      throw error;
    } finally {
      gamesLoading.value = false;
    }
  };

  // 获取游戏详情
  const fetchGameById = async (id: string) => {
    try {
      loading.value = true;
      const response = await gameApi.getGameById(id);
      currentGame.value = response;
      return response;
    } catch (error) {
      console.error('获取游戏详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 创建游戏
  const createGame = async (data: CreateGameRequest) => {
    try {
      loading.value = true;
      const response = await gameApi.createGame(data);
      // 重新获取游戏列表
      await fetchGames();
      await fetchActiveGames();
      return response;
    } catch (error) {
      console.error('创建游戏失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 更新游戏
  const updateGame = async (id: string, data: UpdateGameRequest) => {
    try {
      loading.value = true;
      const response = await gameApi.updateGame(id, data);
      // 更新本地数据
      const index = games.value.findIndex(game => game.id === id);
      if (index !== -1) {
        games.value[index] = response;
      }
      if (currentGame.value?.id === id) {
        currentGame.value = response;
      }
      // 重新获取活跃游戏列表
      await fetchActiveGames();
      return response;
    } catch (error) {
      console.error('更新游戏失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 删除游戏
  const deleteGame = async (id: string) => {
    try {
      loading.value = true;
      const response = await gameApi.deleteGame(id);
      // 从本地数据中移除
      games.value = games.value.filter(game => game.id !== id);
      activeGames.value = activeGames.value.filter(game => game.id !== id);
      if (currentGame.value?.id === id) {
        currentGame.value = null;
      }
      return response;
    } catch (error) {
      console.error('删除游戏失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取指定游戏的段位列表
  const fetchGameRanksByGameId = async (gameId: string) => {
    try {
      ranksLoading.value = true;
      const response = await gameRankApi.getGameRanksByGameId(gameId);
      currentGameRanks.value = response;
      return response;
    } catch (error) {
      console.error('获取游戏段位列表失败:', error);
      throw error;
    } finally {
      ranksLoading.value = false;
    }
  };

  // 获取游戏段位列表
  const fetchGameRanks = async (query?: GameRankQuery) => {
    try {
      ranksLoading.value = true;
      const response = await gameRankApi.getGameRanks(query);
      gameRanks.value = response.items;
      ranksPagination.value = response.pagination;
      return response;
    } catch (error) {
      console.error('获取游戏段位列表失败:', error);
      throw error;
    } finally {
      ranksLoading.value = false;
    }
  };

  // 创建游戏段位
  const createGameRank = async (data: CreateGameRankRequest) => {
    try {
      loading.value = true;
      const response = await gameRankApi.createGameRank(data);
      // 重新获取段位列表
      await fetchGameRanks({ gameId: data.gameId });
      if (data.gameId === currentGameRanks.value[0]?.gameId) {
        await fetchGameRanksByGameId(data.gameId);
      }
      return response;
    } catch (error) {
      console.error('创建游戏段位失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 更新游戏段位
  const updateGameRank = async (id: string, data: UpdateGameRankRequest) => {
    try {
      loading.value = true;
      const response = await gameRankApi.updateGameRank(id, data);
      // 更新本地数据
      const index = gameRanks.value.findIndex(rank => rank.id === id);
      if (index !== -1) {
        gameRanks.value[index] = response;
      }
      const currentIndex = currentGameRanks.value.findIndex(rank => rank.id === id);
      if (currentIndex !== -1) {
        // 重新获取当前游戏段位列表
        await fetchGameRanksByGameId(response.gameId);
      }
      return response;
    } catch (error) {
      console.error('更新游戏段位失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取游戏段位详情
  const fetchGameRankById = async (id: string) => {
    try {
      loading.value = true;
      const response = await gameRankApi.getGameRankById(id);
      return response;
    } catch (error) {
      console.error('获取游戏段位详情失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 删除游戏段位
  const deleteGameRank = async (id: string) => {
    try {
      loading.value = true;
      const response = await gameRankApi.deleteGameRank(id);
      // 从本地数据中移除
      gameRanks.value = gameRanks.value.filter(rank => rank.id !== id);
      currentGameRanks.value = currentGameRanks.value.filter(rank => rank.id !== id);
      return response;
    } catch (error) {
      console.error('删除游戏段位失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 获取游戏统计信息
  const fetchGameStatistics = async (gameId: string) => {
    try {
      statisticsLoading.value = true;
      const response = await gameApi.getGameStatistics(gameId);
      gameStatistics.value = response;
      return response;
    } catch (error) {
      console.error('获取游戏统计信息失败:', error);
      throw error;
    } finally {
      statisticsLoading.value = false;
    }
  };

  // 获取游戏热度分析
  const fetchGameHotness = async () => {
    try {
      loading.value = true;
      const response = await gameApi.getGameHotness();
      gameHotness.value = response;
      return response;
    } catch (error) {
      console.error('获取游戏热度分析失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 计算订单价格
  const calculateOrderPrice = async (data: {
    gameId: string;
    currentRankId: string;
    targetRankId: string;
    priority?: string;
  }) => {
    try {
      loading.value = true;
      const response = await gameApi.calculateOrderPrice(data);
      priceCalculation.value = response;
      return response;
    } catch (error) {
      console.error('计算订单价格失败:', error);
      throw error;
    } finally {
      loading.value = false;
    }
  };

  // 清空当前游戏段位
  const clearCurrentGameRanks = () => {
    currentGameRanks.value = [];
  };

  // 重置状态
  const resetState = () => {
    games.value = [];
    activeGames.value = [];
    currentGame.value = null;
    gameRanks.value = [];
    currentGameRanks.value = [];
    gameStatistics.value = null;
    gameHotness.value = [];
    priceCalculation.value = null;
    loading.value = false;
    gamesLoading.value = false;
    ranksLoading.value = false;
    statisticsLoading.value = false;
  };

  return {
    // 状态
    games,
    activeGames,
    currentGame,
    gameRanks,
    currentGameRanks,
    gameStatistics,
    gameHotness,
    priceCalculation,
    loading,
    gamesLoading,
    ranksLoading,
    statisticsLoading,
    gamesPagination,
    ranksPagination,
    
    // 计算属性
    gameOptions,
    rankOptions,
    
    // 方法
    fetchActiveGames,
    fetchGames,
    fetchGameById,
    createGame,
    updateGame,
    deleteGame,
    fetchGameRanksByGameId,
    fetchGameRanks,
    fetchGameRankById,
    createGameRank,
    updateGameRank,
    deleteGameRank,
    fetchGameStatistics,
    fetchGameHotness,
    calculateOrderPrice,
    clearCurrentGameRanks,
    resetState
  };
});
