<template>
  <div class="create-order-container">
    <div class="page-header">
      <h1>创建订单</h1>
      <el-button @click="$router.back()">返回</el-button>
    </div>

    <el-card class="create-order-card">
      <!-- 游戏选择 -->
      <div v-if="!selectedGame" class="game-selection">
        <h3 class="section-title">选择游戏</h3>

        <!-- 游戏列表 -->
        <div v-if="activeGames.length > 0">
          <el-row :gutter="16">
            <el-col
              v-for="game in activeGames"
              :key="game.id"
              :span="6"
              class="game-card-col"
            >
              <el-card
                class="game-card"
                shadow="hover"
                @click="selectGame(game)"
              >
                <div class="game-content">
                  <el-avatar
                    :src="game.icon"
                    :alt="game.displayName"
                    shape="square"
                    size="large"
                  >
                    {{ game.displayName.charAt(0) }}
                  </el-avatar>
                  <h4>{{ game.displayName }}</h4>
                  <p class="game-desc">{{ game.description }}</p>
                  <div class="game-stats">
                    <el-tag size="small" type="info">
                      {{ game._count?.formFields || 0 }} 个字段
                    </el-tag>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 无游戏数据时显示基础表单 -->
        <div v-else-if="!loading" class="no-games-fallback">
          <el-alert
            title="无法加载游戏列表"
            type="warning"
            :closable="false"
            show-icon
            style="margin-bottom: 20px;"
          >
            <template #default>
              无法获取游戏数据，您可以使用基础表单创建订单
            </template>
          </el-alert>

          <!-- 基础表单（无游戏选择时的备用方案） -->
          <el-form
            ref="fallbackFormRef"
            :model="basicFormData"
            :rules="basicFormRules"
            label-width="120px"
            class="fallback-form"
          >
            <el-form-item label="客户姓名" prop="customerName">
              <el-input
                v-model="basicFormData.customerName"
                placeholder="请输入客户姓名"
                clearable
              />
            </el-form-item>

            <el-form-item label="联系方式" prop="customerContact">
              <el-input
                v-model="basicFormData.customerContact"
                placeholder="请输入客户联系方式（可选）"
                clearable
              />
            </el-form-item>

            <el-form-item label="游戏账号" prop="gameAccount">
              <el-input
                v-model="basicFormData.gameAccount"
                placeholder="请输入游戏账号"
                clearable
              />
            </el-form-item>

            <el-form-item label="游戏密码" prop="gamePassword">
              <el-input
                v-model="basicFormData.gamePassword"
                type="password"
                show-password
                placeholder="请输入游戏密码"
                clearable
              />
            </el-form-item>

            <el-form-item label="订单价格" prop="price">
              <el-input-number
                v-model="basicFormData.price"
                :min="0.01"
                :precision="2"
                placeholder="请输入订单价格"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="特殊要求" prop="requirements">
              <el-input
                v-model="basicFormData.requirements"
                type="textarea"
                :rows="3"
                placeholder="请输入特殊要求（可选）"
              />
            </el-form-item>

            <!-- 提交按钮 -->
            <el-form-item>
              <el-button type="primary" @click="handleFallbackSubmit" :loading="submitting">
                创建订单
              </el-button>
              <el-button @click="resetBasicForm">重置</el-button>
              <el-button type="info" @click="loadActiveGames">
                重新加载游戏
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-tip">
          <el-icon class="is-loading"><Loading /></el-icon>
          正在加载游戏列表...
        </div>
      </div>

      <!-- 订单创建表单 -->
      <div v-else class="order-form-container">
        <div class="form-header">
          <div class="game-info">
            <el-avatar
              :src="selectedGame.icon"
              :alt="selectedGame.displayName"
              shape="square"
              size="default"
            >
              {{ selectedGame.displayName.charAt(0) }}
            </el-avatar>
            <div class="game-details">
              <h3>{{ selectedGame.displayName }}</h3>
              <p>{{ selectedGame.description }}</p>
            </div>
          </div>
          <el-button @click="changeGame" type="text">
            重新选择游戏
          </el-button>
        </div>



        <!-- 字段管理工具栏 -->
        <div v-if="gameFormFields.length > 0" class="field-management-toolbar">
          <el-button
            type="primary"
            :icon="Plus"
            @click="showFieldConfigDialog = true"
            size="small"
          >
            添加字段
          </el-button>
          <el-button
            type="info"
            size="small"
            @click="loadGameFormFields(selectedGame.id)"
          >
            刷新字段
          </el-button>
        </div>

        <!-- 动态表单 -->
        <div v-if="gameFormFields.length > 0" class="dynamic-form-container">
          <el-form
            ref="dynamicFormRef"
            :model="formData"
            label-width="120px"
            class="dynamic-form"
          >
            <div class="form-layout">
              <!-- 左侧：表单字段 -->
              <div class="form-fields-section">
                <h4 class="section-title">订单信息</h4>
            <!-- 字段容器 -->
          <div
            v-for="field in gameFormFields"
            :key="field.id"
            class="field-container"
          >
            <!-- 字段标题行 -->
            <div class="field-header">
              <label class="field-label">
                {{ field.fieldLabel }}
                <span v-if="field.isRequired" class="required-mark">*</span>
              </label>
              <div class="field-actions">
                <el-button
                  type="text"
                  size="small"
                  @click="editField(field)"
                  class="field-action-btn"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="deleteField(field)"
                  class="field-action-btn delete-btn"
                >
                  删除
                </el-button>
              </div>
            </div>

            <!-- 字段输入控件 -->
            <!-- 单行文本框 -->
            <el-input
              v-if="field.fieldType === 'TEXT'"
              v-model="formData[field.fieldKey]"
              :placeholder="field.placeholder || `请输入${field.fieldLabel}`"
              :maxlength="field.config?.maxLength"
              clearable
            />

            <!-- 多行文本框 -->
            <el-input
              v-else-if="field.fieldType === 'TEXTAREA'"
              v-model="formData[field.fieldKey]"
              type="textarea"
              :rows="field.config?.rows || 3"
              :placeholder="field.placeholder || `请输入${field.fieldLabel}`"
              :maxlength="field.config?.maxLength"
            />

            <!-- 下拉选择框 -->
            <el-select
              v-else-if="field.fieldType === 'SELECT'"
              v-model="formData[field.fieldKey]"
              :placeholder="field.placeholder || `请选择${field.fieldLabel}`"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="option in field.options"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>

            <!-- 多选框 -->
            <el-checkbox-group
              v-else-if="field.fieldType === 'CHECKBOX'"
              v-model="formData[field.fieldKey]"
            >
              <el-checkbox
                v-for="option in field.options"
                :key="option"
                :label="option"
              >
                {{ option }}
              </el-checkbox>
            </el-checkbox-group>

            <!-- 数字输入框 -->
            <el-input-number
              v-else-if="field.fieldType === 'NUMBER'"
              v-model="formData[field.fieldKey]"
              :min="field.config?.min"
              :max="field.config?.max"
              :step="field.config?.step || 1"
              :placeholder="field.placeholder"
              style="width: 100%"
            />

            <!-- 密码框 -->
            <el-input
              v-else-if="field.fieldType === 'PASSWORD'"
              v-model="formData[field.fieldKey]"
              type="password"
              :placeholder="field.placeholder || `请输入${field.fieldLabel}`"
              show-password
              clearable
            />

            <!-- 图片上传 -->
            <el-upload
              v-else-if="field.fieldType === 'IMAGE'"
              class="image-uploader"
              action="/api/v1/upload"
              :show-file-list="false"
              :on-success="(response) => handleImageUpload(response, field.fieldKey)"
              :before-upload="beforeImageUpload"
            >
              <img v-if="formData[field.fieldKey]" :src="formData[field.fieldKey]" class="uploaded-image" />
              <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </div>

          <!-- 提交按钮 -->
          <el-form-item>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
              创建订单
            </el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
            </div>
          </div>
        </el-form>
        </div>

        <!-- 无字段提示和基础表单 -->
        <div v-else class="no-fields-tip">
          <el-alert
            title="该游戏暂未配置表单字段"
            type="warning"
            :closable="false"
            show-icon
            style="margin-bottom: 20px;"
          >
            <template #default>
              您可以配置字段或使用基础表单创建订单
            </template>
          </el-alert>

          <!-- 基础表单 -->
          <el-form
            ref="basicFormRef"
            :model="basicFormData"
            :rules="basicFormRules"
            label-width="120px"
            class="basic-form"
          >
            <el-form-item label="客户姓名" prop="customerName">
              <el-input
                v-model="basicFormData.customerName"
                placeholder="请输入客户姓名"
                clearable
              />
            </el-form-item>

            <el-form-item label="联系方式" prop="customerContact">
              <el-input
                v-model="basicFormData.customerContact"
                placeholder="请输入客户联系方式（可选）"
                clearable
              />
            </el-form-item>

            <el-form-item label="游戏账号" prop="gameAccount">
              <el-input
                v-model="basicFormData.gameAccount"
                placeholder="请输入游戏账号"
                clearable
              />
            </el-form-item>

            <el-form-item label="游戏密码" prop="gamePassword">
              <el-input
                v-model="basicFormData.gamePassword"
                type="password"
                show-password
                placeholder="请输入游戏密码"
                clearable
              />
            </el-form-item>

            <el-form-item label="订单价格" prop="price">
              <el-input-number
                v-model="basicFormData.price"
                :min="0.01"
                :precision="2"
                placeholder="请输入订单价格"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="特殊要求" prop="requirements">
              <el-input
                v-model="basicFormData.requirements"
                type="textarea"
                :rows="3"
                placeholder="请输入特殊要求（可选）"
              />
            </el-form-item>

            <!-- 提交按钮 -->
            <el-form-item>
              <el-button type="primary" @click="handleBasicSubmit" :loading="submitting">
                创建订单
              </el-button>
              <el-button @click="resetBasicForm">重置</el-button>
              <el-button type="info" @click="showFieldConfigDialog = true">
                配置字段
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-card>

    <!-- 字段配置对话框 -->
    <el-dialog
      v-model="showFieldConfigDialog"
      title="配置游戏字段"
      width="80%"
      :close-on-click-modal="false"
    >
      <div class="field-config-content">
        <el-alert
          title="快速配置字段"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px;"
        >
          <template #default>
            为 {{ selectedGame?.displayName }} 添加表单字段，配置完成后即可创建订单
          </template>
        </el-alert>

        <el-button type="primary" @click="addQuickField('customer_name', '客户姓名', 'TEXT', true)">
          + 客户姓名
        </el-button>
        <el-button type="primary" @click="addQuickField('customer_contact', '联系方式', 'TEXT', true)">
          + 联系方式
        </el-button>
        <el-button type="primary" @click="addQuickField('game_account', '游戏账号', 'TEXT', true)">
          + 游戏账号
        </el-button>
        <el-button type="primary" @click="addQuickField('game_password', '游戏密码', 'PASSWORD', false)">
          + 游戏密码
        </el-button>
        <el-button type="primary" @click="addQuickField('requirements', '需求说明', 'TEXTAREA', false)">
          + 需求说明
        </el-button>
        <el-button type="primary" @click="addQuickField('price', '价格', 'NUMBER', false)">
          + 价格
        </el-button>
        <el-button type="success" @click="addQuickField('server_region', '服务器', 'SELECT', false)">
          + 服务器
        </el-button>
        <el-button type="success" @click="addQuickField('delivery_time', '交付时间', 'TEXT', false)">
          + 交付时间
        </el-button>
        <el-button type="success" @click="addQuickField('payment_method', '支付方式', 'SELECT', false)">
          + 支付方式
        </el-button>
        <el-button type="success" @click="addQuickField('order_notes', '订单备注', 'TEXTAREA', false)">
          + 订单备注
        </el-button>

        <el-divider content-position="left">自定义字段</el-divider>

        <!-- 自定义字段创建表单 -->
        <el-form
          ref="customFieldFormRef"
          :model="customFieldForm"
          :rules="customFieldRules"
          label-width="100px"
          style="max-width: 600px;"
        >
          <el-form-item label="字段键名" prop="fieldKey">
            <el-input
              v-model="customFieldForm.fieldKey"
              placeholder="如: custom_field_1"
              @input="validateFieldKey"
            />
            <div class="field-help-text">字段键名用于数据存储，只能包含字母、数字和下划线</div>
          </el-form-item>

          <el-form-item label="字段标签" prop="fieldLabel">
            <el-input
              v-model="customFieldForm.fieldLabel"
              placeholder="如: 自定义字段"
            />
            <div class="field-help-text">字段标签是用户看到的名称</div>
          </el-form-item>

          <el-form-item label="字段类型" prop="fieldType">
            <el-select v-model="customFieldForm.fieldType" placeholder="请选择字段类型">
              <el-option label="单行文本框" value="TEXT" />
              <el-option label="多行文本框" value="TEXTAREA" />
              <el-option label="数字输入框" value="NUMBER" />
              <el-option label="密码框" value="PASSWORD" />
              <el-option label="下拉选择框" value="SELECT" />
              <el-option label="多选框" value="CHECKBOX" />
            </el-select>
          </el-form-item>

          <el-form-item label="占位符" prop="placeholder">
            <el-input
              v-model="customFieldForm.placeholder"
              placeholder="如: 请输入..."
            />
          </el-form-item>

          <el-form-item label="是否必填">
            <el-switch v-model="customFieldForm.isRequired" />
          </el-form-item>

          <!-- 下拉选择框和多选框的选项配置 -->
          <el-form-item
            v-if="customFieldForm.fieldType === 'SELECT' || customFieldForm.fieldType === 'CHECKBOX'"
            label="选项配置"
          >
            <div class="options-config">
              <el-input
                v-for="(option, index) in customFieldForm.options"
                :key="index"
                v-model="customFieldForm.options[index]"
                placeholder="输入选项"
                style="margin-bottom: 8px;"
              >
                <template #append>
                  <el-button
                    @click="removeOption(index)"
                    type="danger"
                    :icon="Delete"
                    size="small"
                  />
                </template>
              </el-input>
              <el-button @click="addOption" type="primary" size="small">
                添加选项
              </el-button>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="createCustomField">
              创建字段
            </el-button>
            <el-button @click="resetCustomFieldForm">
              重置
            </el-button>
          </el-form-item>
        </el-form>

        <div style="margin-top: 20px;">
          <el-button @click="showFieldConfigDialog = false">关闭</el-button>
          <el-button type="primary" @click="handleFieldUpdated">完成配置</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Loading, Delete, Sort } from '@element-plus/icons-vue'
import { getActiveGames } from '../../api/game'
import { getActiveFieldsByGameId, createGameFormField, deleteGameFormField, updateFieldsOrder } from '../../api/gameFormFields'
import draggable from 'vuedraggable'
import { orderApi } from '../../api/orders'
import { OrderPriority } from '../../types'
// import FormFieldConfigDialog from '../../components/FormFieldConfigDialog.vue'

// 类型定义
interface Game {
  id: string
  name: string
  displayName: string
  description?: string
  icon?: string
  isActive: boolean
  sortOrder: number
  _count?: {
    orders: number
    employeeSkills: number
    formFields: number
  }
}

interface GameFormField {
  id: string
  gameId: string
  fieldKey: string
  fieldLabel: string
  fieldType: 'TEXT' | 'TEXTAREA' | 'SELECT' | 'CHECKBOX' | 'NUMBER' | 'PASSWORD' | 'IMAGE'
  isRequired: boolean
  placeholder?: string
  sortOrder: number
  options?: string[]
  config?: Record<string, any>
  isActive: boolean
}

const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const activeGames = ref<Game[]>([])
const selectedGame = ref<Game | null>(null)
const gameFormFields = ref<GameFormField[]>([])
const formData = ref<Record<string, any>>({})
const formRef = ref()
const showFieldConfigDialog = ref(false)
const customFieldFormRef = ref()

// 自定义字段表单数据
const customFieldForm = ref({
  fieldKey: '',
  fieldLabel: '',
  fieldType: 'TEXT',
  placeholder: '',
  isRequired: false,
  options: ['']
})

// 自定义字段验证规则
const customFieldRules = {
  fieldKey: [
    { required: true, message: '请输入字段键名', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段键名只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' }
  ],
  fieldLabel: [
    { required: true, message: '请输入字段标签', trigger: 'blur' }
  ],
  fieldType: [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ]
}

// 基础表单数据
const basicFormData = ref({
  customerName: '',
  customerContact: '',
  gameAccount: '',
  gamePassword: '',
  price: 0,
  requirements: ''
})
const basicFormRef = ref()
const fallbackFormRef = ref()

// 基础表单验证规则
const basicFormRules = {
  customerName: [
    { required: true, message: '请输入客户姓名', trigger: 'blur' },
    { min: 1, max: 50, message: '客户姓名长度应在1-50个字符之间', trigger: 'blur' }
  ],
  gameAccount: [
    { required: true, message: '请输入游戏账号', trigger: 'blur' },
    { min: 1, max: 100, message: '游戏账号长度应在1-100个字符之间', trigger: 'blur' }
  ],
  gamePassword: [
    { required: true, message: '请输入游戏密码', trigger: 'blur' },
    { min: 1, max: 100, message: '游戏密码长度应在1-100个字符之间', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入订单价格', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '订单价格必须大于0', trigger: 'blur' }
  ]
}

// 计算属性
const dynamicFormRules = computed(() => {
  const rules: Record<string, any[]> = {}
  
  gameFormFields.value.forEach(field => {
    if (field.isRequired) {
      rules[field.fieldKey] = [
        {
          required: true,
          message: `请输入${field.fieldLabel}`,
          trigger: field.fieldType === 'SELECT' ? 'change' : 'blur'
        }
      ]
    }
  })
  
  return rules
})

// 方法
const loadActiveGames = async () => {
  try {
    loading.value = true
    console.log('开始加载游戏列表...')
    const games = await getActiveGames()
    console.log('获取到的游戏列表:', games)
    activeGames.value = games || []

    if (!games || games.length === 0) {
      console.warn('没有找到活跃的游戏')
      ElMessage.warning('暂无可用游戏，请联系管理员添加游戏')
    }
  } catch (error) {
    console.error('加载游戏列表失败:', error)
    ElMessage.error(`加载游戏列表失败: ${error.message || '未知错误'}`)
    // 如果API调用失败，显示基础表单
    activeGames.value = []
  } finally {
    loading.value = false
  }
}

const selectGame = async (game: Game) => {
  console.log('选择游戏:', game)
  selectedGame.value = game
  console.log('selectedGame.value 已设置:', selectedGame.value)
  await loadGameFormFields(game.id)
}

const loadGameFormFields = async (gameId: string) => {
  try {
    loading.value = true
    console.log('开始加载游戏字段，gameId:', gameId)
    gameFormFields.value = await getActiveFieldsByGameId(gameId)
    console.log('获取到的游戏字段:', gameFormFields.value)
    console.log('字段数量:', gameFormFields.value.length)
    initFormData()
    console.log('初始化后的表单数据:', formData.value)
  } catch (error) {
    console.error('加载游戏字段失败:', error)
    ElMessage.error('加载游戏字段失败')
  } finally {
    loading.value = false
  }
}

const initFormData = () => {
  const data: Record<string, any> = {}
  
  gameFormFields.value.forEach(field => {
    if (field.fieldType === 'CHECKBOX') {
      data[field.fieldKey] = []
    } else if (field.fieldType === 'NUMBER') {
      data[field.fieldKey] = field.config?.min || 0
    } else {
      data[field.fieldKey] = ''
    }
  })
  
  formData.value = data
}

const changeGame = () => {
  selectedGame.value = null
  gameFormFields.value = []
  formData.value = {}
}

const resetForm = () => {
  formRef.value?.resetFields()
  initFormData()
}

const handleImageUpload = (response: any, fieldKey: string) => {
  if (response.success) {
    formData.value[fieldKey] = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

const addQuickField = async (fieldKey: string, fieldLabel: string, fieldType: string, isRequired: boolean) => {
  if (!selectedGame.value) return

  try {
    // 检查字段是否已存在
    const existingField = gameFormFields.value.find(field => field.fieldKey === fieldKey)
    if (existingField) {
      ElMessage.warning(`字段 "${fieldLabel}" 已存在`)
      return
    }

    await createGameFormField({
      gameId: selectedGame.value.id,
      fieldKey,
      fieldLabel,
      fieldType: fieldType as any,
      isRequired,
      placeholder: `请输入${fieldLabel}`,
      sortOrder: gameFormFields.value.length + 1,
      isActive: true
    })

    ElMessage.success(`字段 "${fieldLabel}" 添加成功`)

    // 重新加载字段列表
    await loadGameFormFields(selectedGame.value.id)
  } catch (error) {
    console.error('添加字段失败:', error)
    ElMessage.error(`添加字段 "${fieldLabel}" 失败`)
  }
}

const handleFieldUpdated = async () => {
  // 字段配置更新后，重新加载字段
  if (selectedGame.value) {
    await loadGameFormFields(selectedGame.value.id)
    showFieldConfigDialog.value = false
    ElMessage.success('字段配置已更新')
  }
}

const handleSubmit = async () => {
  try {
    // 表单验证
    await formRef.value.validate()

    submitting.value = true

    console.log('提交表单数据:', formData.value)
    console.log('选中的游戏:', selectedGame.value)

    // 从动态表单数据中提取必需的标准字段
    const extractFieldValue = (possibleKeys: string[]) => {
      for (const key of possibleKeys) {
        if (formData.value[key] !== undefined && formData.value[key] !== null && formData.value[key] !== '') {
          return formData.value[key]
        }
      }
      return undefined
    }

    // 构建订单数据 - 按照新思路要求
    const orderData = {
      gameId: selectedGame.value!.id || '',
      gameType: selectedGame.value!.name,
      // 核心：将表单数据存储到details字段（新思路的核心实现）
      details: formData.value,
      formData: formData.value, // 保持向后兼容
      // 从表单数据中提取标准字段 - 支持多种可能的字段名
      customerName: extractFieldValue(['customer_name', 'customerName', 'name']),
      customerContact: extractFieldValue(['customer_contact', 'customerContact', 'contact', 'phone']),
      gameAccount: extractFieldValue(['game_account', 'gameAccount', 'account']),
      gamePassword: extractFieldValue(['game_password', 'gamePassword', 'password']),
      price: extractFieldValue(['price', 'total_price', 'amount']) || 0,
      requirements: extractFieldValue(['requirements', 'notes', 'remark', 'description']),
      // 添加必需的字段以满足OrderForm类型要求
      currentRankId: extractFieldValue(['current_rank_id', 'currentRankId']) || '',
      targetRankId: extractFieldValue(['target_rank_id', 'targetRankId']) || '',
      priority: OrderPriority.NORMAL
    }

    console.log('构建的订单数据:', orderData)

    // 验证必需字段
    if (!orderData.customerName) {
      throw new Error('客户姓名是必需的')
    }
    if (!orderData.gameAccount) {
      throw new Error('游戏账号是必需的')
    }
    if (!orderData.gamePassword) {
      throw new Error('游戏密码是必需的')
    }
    if (!orderData.price || orderData.price <= 0) {
      throw new Error('订单价格必须大于0')
    }

    await orderApi.createOrder(orderData)
    ElMessage.success('订单创建成功')
    router.push('/boss/orders')
  } catch (error) {
    console.error('创建订单失败:', error)
    ElMessage.error(`创建订单失败: ${error.message || '未知错误'}`)
  } finally {
    submitting.value = false
  }
}

// 基础表单提交
const handleBasicSubmit = async () => {
  try {
    // 表单验证
    await basicFormRef.value.validate()

    submitting.value = true

    console.log('提交基础表单数据:', basicFormData.value)
    console.log('选中的游戏:', selectedGame.value)

    // 构建订单数据
    const orderData = {
      gameId: selectedGame.value?.id || '',
      gameType: selectedGame.value?.name || 'unknown',
      // 核心：将表单数据存储到details字段
      details: basicFormData.value,
      formData: basicFormData.value, // 保持向后兼容
      // 标准字段
      customerName: basicFormData.value.customerName,
      customerContact: basicFormData.value.customerContact,
      gameAccount: basicFormData.value.gameAccount,
      gamePassword: basicFormData.value.gamePassword,
      price: basicFormData.value.price,
      requirements: basicFormData.value.requirements,
      // 添加必需的字段以满足OrderForm类型要求
      currentRankId: '',
      targetRankId: '',
      priority: OrderPriority.NORMAL
    }

    console.log('构建的基础订单数据:', orderData)

    await orderApi.createOrder(orderData)
    ElMessage.success('订单创建成功')
    router.push('/boss/orders')
  } catch (error: any) {
    console.error('创建订单失败:', error)
    ElMessage.error(`创建订单失败: ${error.message || '未知错误'}`)
  } finally {
    submitting.value = false
  }
}

// 重置基础表单
const resetBasicForm = () => {
  basicFormRef.value?.resetFields()
  fallbackFormRef.value?.resetFields()
  basicFormData.value = {
    customerName: '',
    customerContact: '',
    gameAccount: '',
    gamePassword: '',
    price: 0,
    requirements: ''
  }
}

// 备用表单提交（当无法加载游戏时）
const handleFallbackSubmit = async () => {
  try {
    // 表单验证
    await fallbackFormRef.value.validate()

    submitting.value = true

    console.log('提交备用表单数据:', basicFormData.value)

    // 构建订单数据（无游戏选择的情况）
    const orderData = {
      gameId: '',
      gameType: 'unknown',
      // 核心：将表单数据存储到details字段
      details: basicFormData.value,
      formData: basicFormData.value, // 保持向后兼容
      // 标准字段
      customerName: basicFormData.value.customerName,
      customerContact: basicFormData.value.customerContact,
      gameAccount: basicFormData.value.gameAccount,
      gamePassword: basicFormData.value.gamePassword,
      price: basicFormData.value.price,
      requirements: basicFormData.value.requirements,
      // 添加必需的字段以满足OrderForm类型要求
      currentRankId: '',
      targetRankId: '',
      priority: OrderPriority.NORMAL
    }

    console.log('构建的备用订单数据:', orderData)

    await orderApi.createOrder(orderData)
    ElMessage.success('订单创建成功')
    router.push('/boss/orders')
  } catch (error: any) {
    console.error('创建订单失败:', error)
    ElMessage.error(`创建订单失败: ${error.message || '未知错误'}`)
  } finally {
    submitting.value = false
  }
}

// 自定义字段管理功能
const validateFieldKey = () => {
  // 实时验证字段键名是否已存在
  const fieldKey = customFieldForm.value.fieldKey
  if (fieldKey && gameFormFields.value.some(field => field.fieldKey === fieldKey)) {
    ElMessage.warning('字段键名已存在，请使用其他名称')
  }
}

const addOption = () => {
  customFieldForm.value.options.push('')
}

const removeOption = (index: number) => {
  if (customFieldForm.value.options.length > 1) {
    customFieldForm.value.options.splice(index, 1)
  }
}

const resetCustomFieldForm = () => {
  customFieldForm.value = {
    fieldKey: '',
    fieldLabel: '',
    fieldType: 'TEXT',
    placeholder: '',
    isRequired: false,
    options: ['']
  }
  customFieldFormRef.value?.clearValidate()
}

const createCustomField = async () => {
  if (!selectedGame.value) {
    ElMessage.error('请先选择游戏')
    return
  }

  try {
    // 表单验证
    await customFieldFormRef.value.validate()

    // 检查字段键名是否已存在
    const existingField = gameFormFields.value.find(field => field.fieldKey === customFieldForm.value.fieldKey)
    if (existingField) {
      ElMessage.error('字段键名已存在，请使用其他名称')
      return
    }

    // 准备字段数据
    const fieldData: any = {
      gameId: selectedGame.value.id,
      fieldKey: customFieldForm.value.fieldKey,
      fieldLabel: customFieldForm.value.fieldLabel,
      fieldType: customFieldForm.value.fieldType,
      isRequired: customFieldForm.value.isRequired,
      placeholder: customFieldForm.value.placeholder || `请输入${customFieldForm.value.fieldLabel}`,
      sortOrder: gameFormFields.value.length + 1,
      isActive: true
    }

    // 如果是选择框类型，添加选项
    if (customFieldForm.value.fieldType === 'SELECT' || customFieldForm.value.fieldType === 'CHECKBOX') {
      const validOptions = customFieldForm.value.options.filter(option => option.trim() !== '')
      if (validOptions.length === 0) {
        ElMessage.error('请至少添加一个选项')
        return
      }
      fieldData.options = validOptions
    }

    // 创建字段
    await createGameFormField(fieldData)
    ElMessage.success(`字段 "${customFieldForm.value.fieldLabel}" 创建成功`)

    // 重置表单并重新加载字段
    resetCustomFieldForm()
    await loadGameFormFields(selectedGame.value.id)

  } catch (error: any) {
    console.error('创建自定义字段失败:', error)
    ElMessage.error(`创建字段失败: ${error.message || '未知错误'}`)
  }
}

// 字段管理功能
const editField = (field: any) => {
  console.log('编辑字段:', field)
  // 填充编辑表单
  customFieldForm.value = {
    fieldKey: field.fieldKey,
    fieldLabel: field.fieldLabel,
    fieldType: field.fieldType,
    placeholder: field.placeholder || '',
    isRequired: field.isRequired,
    options: field.options || ['']
  }
  ElMessage.info('请在下方自定义字段表单中修改，然后点击"创建字段"来更新')
}

const deleteField = async (field: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字段"${field.fieldLabel}"吗？删除后将无法恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    console.log('删除字段:', field)

    // 执行删除操作
    await deleteGameFormField(field.id)
    ElMessage.success(`字段"${field.fieldLabel}"已删除`)

    // 重新加载字段列表
    if (selectedGame.value) {
      await loadGameFormFields(selectedGame.value.id)
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除字段失败:', error)
      ElMessage.error(`删除字段失败: ${error.message || '未知错误'}`)
    }
  }
}

// 生命周期
onMounted(() => {
  loadActiveGames()
})
</script>

<style scoped>
/* 字段管理工具栏 */
.field-management-toolbar {
  margin-bottom: 24px;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  display: flex;
  gap: 12px;
  align-items: center;
}

.field-management-toolbar .el-button {
  margin: 0;
}

/* 动态表单样式 */
.dynamic-form {
  max-width: 800px;
}

/* 字段容器 */
.field-container {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #ffffff;
  transition: border-color 0.2s ease;
}

.field-container:hover {
  border-color: #c0c4cc;
}

/* 字段标题行 */
.field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
}

.required-mark {
  color: #f56c6c;
  margin-left: 4px;
}

/* 字段操作按钮 */
.field-actions {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.field-container:hover .field-actions {
  opacity: 1;
}

.field-action-btn {
  padding: 4px 8px !important;
  font-size: 12px !important;
  height: 24px !important;
  line-height: 1 !important;
  border: 1px solid transparent !important;
  border-radius: 4px !important;
  background: transparent !important;
  color: #409eff !important;
  cursor: pointer;
}

.field-action-btn:hover {
  background: #ecf5ff !important;
  border-color: #b3d8ff !important;
}

.delete-btn {
  color: #f56c6c !important;
}

.delete-btn:hover {
  background: #fef0f0 !important;
  border-color: #fbc4c4 !important;
}

/* 字段输入区域 */
.field-input {
  width: 100%;
}

/* 调试信息面板样式 */
.debug-info {
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
}

.debug-info p {
  margin: 2px 0;
}

/* 字段配置对话框样式 */
.field-config-content .el-button {
  margin: 5px;
}

.field-config-content .el-button + .el-button {
  margin-left: 5px;
}

/* 自定义字段表单样式 */
.field-help-text {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.options-config {
  width: 100%;
}

.options-config .el-input {
  margin-bottom: 8px;
}

.options-config .el-button {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .field-label-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .field-actions {
    opacity: 1;
    align-self: flex-end;
  }

  .dynamic-form .el-form-item__label {
    text-align: left !important;
    padding-right: 0 !important;
    margin-bottom: 4px;
  }
}

/* 确保表单输入框样式正常 */
.dynamic-form .el-input,
.dynamic-form .el-select,
.dynamic-form .el-textarea {
  width: 100%;
}

.dynamic-form .el-form-item__content {
  line-height: 1.4;
}

/* 原有样式 */
.create-order-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.create-order-card {
  min-height: 500px;
}

.section-title {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 500;
}

.game-card-col {
  margin-bottom: 16px;
}

.game-card {
  cursor: pointer;
  transition: all 0.3s;
}

.game-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.game-content {
  text-align: center;
  padding: 20px;
}

.game-content h4 {
  margin: 12px 0 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.game-desc {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}

.game-stats {
  display: flex;
  justify-content: center;
}

.loading-tip {
  text-align: center;
  padding: 40px;
  color: #666;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
}

.game-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.game-details h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 500;
}

.game-details p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.dynamic-form {
  max-width: 600px;
}

.no-fields-tip {
  text-align: center;
  padding: 60px 20px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
