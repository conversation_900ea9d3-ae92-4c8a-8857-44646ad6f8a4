<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <AppSidebar
      :theme="theme"
      :logo-text="logoText"
      :menu-items="menuItems"
      :collapsed="sidebarCollapsed"
      @menu-click="handleMenuClick"
    />

    <!-- 移动端遮罩层 -->
    <div 
      v-if="isMobile"
      class="nav-overlay"
      :class="{ visible: mobileMenuOpen }"
      @click="closeMobileMenu"
    />

    <!-- 主内容区域 -->
    <div class="nav-main">
      <!-- 顶部导航栏 -->
      <AppHeader
        :collapsed="sidebarCollapsed"
        :show-breadcrumb="showBreadcrumb"
        :show-notifications="showNotifications"
        :custom-breadcrumb="customBreadcrumb"
        @toggle-sidebar="toggleSidebar"
      />

      <!-- 主内容 -->
      <div class="main-content">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../../stores/auth'
import AppSidebar from './AppSidebar.vue'
import AppHeader from './AppHeader.vue'

interface MenuItem {
  index: string
  title: string
  icon: any
  children?: MenuItem[]
  permission?: string[]
}

interface BreadcrumbItem {
  name: string
  path?: string
  current?: boolean
}

interface Props {
  theme: 'boss' | 'employee' | 'admin'
  logoText: string
  menuItems: MenuItem[]
  showBreadcrumb?: boolean
  showNotifications?: boolean
  customBreadcrumb?: BreadcrumbItem[]
}

const props = withDefaults(defineProps<Props>(), {
  showBreadcrumb: true,
  showNotifications: true
})

const authStore = useAuthStore()

// 响应式状态
const sidebarCollapsed = ref(false)
const mobileMenuOpen = ref(false)
const windowWidth = ref(window.innerWidth)

// 计算属性
const isMobile = computed(() => windowWidth.value < 1024)

// 切换侧边栏
const toggleSidebar = () => {
  if (isMobile.value) {
    mobileMenuOpen.value = !mobileMenuOpen.value
  } else {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

// 处理菜单点击
const handleMenuClick = (item: MenuItem) => {
  // 移动端点击菜单项后关闭菜单
  if (isMobile.value) {
    closeMobileMenu()
  }
}

// 窗口大小变化处理
const handleResize = () => {
  windowWidth.value = window.innerWidth
  
  // 桌面端时关闭移动菜单
  if (!isMobile.value) {
    mobileMenuOpen.value = false
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
  
  // 初始化侧边栏状态
  if (isMobile.value) {
    sidebarCollapsed.value = false
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 键盘快捷键支持
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + B 切换侧边栏
  if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
    event.preventDefault()
    toggleSidebar()
  }
  
  // ESC 关闭移动端菜单
  if (event.key === 'Escape' && mobileMenuOpen.value) {
    closeMobileMenu()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style lang="scss" scoped>
@use '@/styles/navigation.scss';

.layout-container {
  position: relative;
}

// 移动端适配
@media (max-width: 1024px) {
  .layout-container {
    :deep(.nav-sidebar) {
      position: fixed;
      left: 0;
      top: 0;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease;

      &.mobile-open {
        transform: translateX(0);
      }
    }

    .nav-main {
      margin-left: 0;
    }
  }
}

// 确保内容区域正确滚动
.main-content {
  height: calc(100vh - 64px);
  overflow-y: auto;
  
  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;

    &:hover {
      background: #94a3b8;
    }
  }
}

// 平滑过渡效果
* {
  box-sizing: border-box;
}

.layout-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}
</style>
