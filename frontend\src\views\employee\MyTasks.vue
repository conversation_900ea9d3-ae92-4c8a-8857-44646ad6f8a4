<template>
  <div class="my-tasks-container">
    <div class="page-header">
      <h1>我的任务</h1>
      <div class="header-actions">
        <el-select v-model="statusFilter" placeholder="筛选状态" clearable style="width: 150px; margin-right: 10px;">
          <el-option label="已接受" value="ACCEPTED" />
          <el-option label="进行中" value="IN_PROGRESS" />
          <el-option label="已暂停" value="PAUSED" />
          <el-option label="已提交" value="SUBMITTED" />
          <el-option label="已审核" value="APPROVED" />
          <el-option label="已拒绝" value="REJECTED" />
          <el-option label="已完成" value="COMPLETED" />
        </el-select>
        <el-button @click="fetchMyTasks" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="tasks-list">
      <el-card
        v-for="task in myTasks"
        :key="task.id"
        class="task-card"
        shadow="hover"
      >
        <div class="task-header">
          <div class="task-info">
            <h3>{{ task.taskNo }}</h3>
            <el-tag :type="getStatusType(task.status)">
              {{ getStatusText(task.status) }}
            </el-tag>
          </div>
          <div class="task-meta">
            <span class="customer">{{ task.order?.customerName }}</span>
            <span class="time">{{ formatDate(task.createdAt) }}</span>
          </div>
        </div>

        <div class="task-content">
          <div class="order-details">
            <div class="game-info" v-if="task.order?.gameType">
              <span class="label">游戏:</span>
              <el-tag type="primary" size="small">
                {{ getGameTypeName(task.order.gameType) }}
              </el-tag>
            </div>

            <div class="commission-info" v-if="task.commission">
              <span class="label">佣金:</span>
              <span class="value commission">¥{{ task.commission }}</span>
            </div>
          </div>

          <div class="task-progress" v-if="task.progress && task.progress.length > 0">
            <div class="progress-item">
              <span class="label">当前进度:</span>
              <span class="value">{{ task.progress[0].progress }}%</span>
            </div>
            <div class="progress-bar">
              <el-progress :percentage="task.progress[0].progress" :show-text="false" />
            </div>
          </div>

          <div class="task-description" v-if="task.description">
            <p>{{ task.description }}</p>
          </div>
        </div>

        <div class="task-actions">
          <el-button
            v-if="task.status === 'ACCEPTED'"
            type="primary"
            @click="handleStartTask(task)"
          >
            开始任务
          </el-button>
          <el-button
            v-if="task.status === 'IN_PROGRESS'"
            type="primary"
            @click="handleUpdateProgress(task)"
          >
            更新进度
          </el-button>
          <el-button
            v-if="task.status === 'IN_PROGRESS'"
            type="warning"
            @click="handlePauseTask(task)"
          >
            暂停任务
          </el-button>
          <el-button
            v-if="task.status === 'IN_PROGRESS'"
            type="success"
            @click="handleSubmitTask(task)"
          >
            提交完成
          </el-button>
          <el-button
            v-if="task.status === 'PAUSED'"
            type="primary"
            @click="handleResumeTask(task)"
          >
            继续任务
          </el-button>
          <el-button @click="handleViewDetails(task)">
            查看详情
          </el-button>
          <el-button
            v-if="task.orderId"
            type="info"
            @click="handleViewOrderDetails(task)"
          >
            查看订单详情
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 空状态 -->
    <el-empty
      v-if="!loading && myTasks.length === 0"
      description="暂无任务"
    />

    <!-- 分页 -->
    <div class="pagination-container" v-if="myTasks.length > 0">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 更新进度对话框 -->
    <UpdateProgressDialog
      v-model="showProgressDialog"
      :task="selectedTask"
      @success="handleProgressUpdateSuccess"
    />

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="showDetailDialog"
      :task-id="selectedTaskId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { taskApi } from '@/api/tasks'
import type { Task, TaskStatus } from '@/types'
import { formatDate } from '@/utils/date'
import UpdateProgressDialog from '@/components/UpdateProgressDialog.vue'
import TaskDetailDialog from '@/components/TaskDetailDialog.vue'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const myTasks = ref<Task[]>([])
const statusFilter = ref<TaskStatus | undefined>(undefined)
const showProgressDialog = ref(false)
const selectedTask = ref<Task | null>(null)
const showDetailDialog = ref(false)
const selectedTaskId = ref<string | null>(null)

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 监听状态筛选变化
watch(statusFilter, () => {
  pagination.page = 1
  fetchMyTasks()
})

// 获取我的任务列表
const fetchMyTasks = async () => {
  try {
    loading.value = true
    const response = await taskApi.getMyTasks({
      status: statusFilter.value,
      page: pagination.page,
      limit: pagination.limit
    })

    if (response.success && response.data) {
      myTasks.value = response.data.items
      pagination.total = response.data.pagination.total
    }
  } catch (error) {
    console.error('获取我的任务失败:', error)
    ElMessage.error('获取我的任务失败')
  } finally {
    loading.value = false
  }
}

// 开始任务
const handleStartTask = async (task: Task) => {
  try {
    await ElMessageBox.confirm(
      `确定要开始任务 ${task.taskNo} 吗？`,
      '确认开始',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const response = await taskApi.updateTaskStatus(task.id, 'IN_PROGRESS')
    if (response.success) {
      ElMessage.success('任务已开始')
      fetchMyTasks()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开始任务失败:', error)
      ElMessage.error('开始任务失败')
    }
  }
}

// 更新进度
const handleUpdateProgress = (task: Task) => {
  selectedTask.value = task
  showProgressDialog.value = true
}

// 暂停任务
const handlePauseTask = async (task: Task) => {
  try {
    await ElMessageBox.confirm(
      `确定要暂停任务 ${task.taskNo} 吗？`,
      '确认暂停',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await taskApi.updateTaskStatus(task.id, 'PAUSED')
    if (response.success) {
      ElMessage.success('任务已暂停')
      fetchMyTasks()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('暂停任务失败:', error)
      ElMessage.error('暂停任务失败')
    }
  }
}

// 继续任务
const handleResumeTask = async (task: Task) => {
  try {
    await ElMessageBox.confirm(
      `确定要继续任务 ${task.taskNo} 吗？`,
      '确认继续',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const response = await taskApi.updateTaskStatus(task.id, 'IN_PROGRESS')
    if (response.success) {
      ElMessage.success('任务已继续')
      fetchMyTasks()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('继续任务失败:', error)
      ElMessage.error('继续任务失败')
    }
  }
}

// 提交任务
const handleSubmitTask = async (task: Task) => {
  try {
    await ElMessageBox.confirm(
      `确定要提交任务 ${task.taskNo} 吗？提交后将等待审核。`,
      '确认提交',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await taskApi.updateTaskStatus(task.id, 'SUBMITTED')
    if (response.success) {
      ElMessage.success('任务已提交，等待审核')
      fetchMyTasks()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('提交任务失败:', error)
      ElMessage.error('提交任务失败')
    }
  }
}

// 查看详情
const handleViewDetails = (task: Task) => {
  selectedTaskId.value = task.id
  showDetailDialog.value = true
}

// 查看订单详情
const handleViewOrderDetails = (task: Task) => {
  if (task.orderId) {
    router.push(`/employee/orders/${task.orderId}`)
  } else {
    ElMessage.warning('该任务没有关联的订单')
  }
}

// 进度更新成功回调
const handleProgressUpdateSuccess = () => {
  showProgressDialog.value = false
  fetchMyTasks()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchMyTasks()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchMyTasks()
}

// 状态相关方法
const getStatusType = (status: TaskStatus) => {
  const statusMap: Record<TaskStatus, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    PENDING: 'warning',
    ACCEPTED: 'info',
    IN_PROGRESS: 'primary',
    PAUSED: 'warning',
    SUBMITTED: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger',
    COMPLETED: 'success',
    CANCELLED: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: TaskStatus) => {
  const statusMap = {
    PENDING: '待处理',
    ACCEPTED: '已接受',
    IN_PROGRESS: '进行中',
    PAUSED: '已暂停',
    SUBMITTED: '已提交',
    APPROVED: '已审核',
    REJECTED: '已拒绝',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

// 游戏类型名称映射
const gameTypeNames: Record<string, string> = {
  'wzry': '王者荣耀',
  'lol': '英雄联盟',
  'ys': '原神',
  'hpjy': '和平精英',
  'pubg': '绝地求生',
  'mc': '鸣潮',
  'csgo': 'CSGO',
  'dota2': 'DOTA2'
}

const getGameTypeName = (gameType: string): string => {
  return gameTypeNames[gameType] || gameType
}

// 初始化
onMounted(() => {
  fetchMyTasks()
})
</script>

<style lang="scss" scoped>
.my-tasks-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    color: var(--text-primary);
  }

  .header-actions {
    display: flex;
    align-items: center;
  }
}

.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.task-card {
  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;

    .task-info {
      display: flex;
      align-items: center;
      gap: 12px;

      h3 {
        margin: 0;
        color: var(--text-primary);
      }
    }

    .task-meta {
      text-align: right;

      .customer {
        display: block;
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 4px;
      }

      .time {
        font-size: 12px;
        color: #909399;
      }
    }
  }

  .task-content {
    margin-bottom: 16px;

    .order-details {
      display: flex;
      gap: 24px;
      margin-bottom: 12px;

      .rank-info, .commission-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .label {
          color: #909399;
          font-size: 14px;
        }

        .value {
          color: var(--text-primary);
          font-weight: 500;

          &.commission {
            color: #f56c6c;
            font-weight: bold;
          }
        }
      }
    }

    .task-progress {
      margin-bottom: 12px;

      .progress-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        .label {
          color: #909399;
          font-size: 14px;
        }

        .value {
          color: var(--text-primary);
          font-weight: 500;
        }
      }

      .progress-bar {
        margin-bottom: 8px;
      }
    }

    .task-description {
      padding: 12px;
      background-color: #f5f7fa;
      border-radius: 4px;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }

  .task-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}
</style>
