<template>
  <div class="login-logs">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon><Document /></el-icon>
          登录日志
        </h1>
        <p class="page-description">查看用户登录历史记录和安全审计信息</p>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          :icon="Refresh" 
          @click="refreshData"
          :loading="monitoringStore.loading"
        >
          刷新数据
        </el-button>
        <el-button 
          type="success" 
          :icon="Download" 
          @click="exportLogs"
        >
          导出日志
        </el-button>
      </div>
    </div>

    <!-- 筛选器 -->
    <el-card class="filter-card">
      <div class="filter-content">
        <el-form :model="filters" inline>
          <el-form-item label="用户名">
            <el-input 
              v-model="filters.username" 
              placeholder="输入用户名" 
              clearable 
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="IP地址">
            <el-input 
              v-model="filters.ipAddress" 
              placeholder="输入IP地址" 
              clearable 
              style="width: 150px"
            />
          </el-form-item>
          <el-form-item label="登录结果">
            <el-select v-model="filters.loginResult" placeholder="选择结果" clearable style="width: 120px">
              <el-option label="成功" value="SUCCESS" />
              <el-option label="失败" value="FAILED" />
              <el-option label="被阻止" value="BLOCKED" />
            </el-select>
          </el-form-item>
          <el-form-item label="省份">
            <el-input 
              v-model="filters.locationProvince" 
              placeholder="输入省份" 
              clearable 
              style="width: 120px"
            />
          </el-form-item>
          <el-form-item label="城市">
            <el-input 
              v-model="filters.locationCity" 
              placeholder="输入城市" 
              clearable 
              style="width: 120px"
            />
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 350px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="applyFilters" :loading="monitoringStore.loading">
              筛选
            </el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon success">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ successCount }}</div>
                <div class="stats-label">成功登录</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon failed">
                <el-icon><Close /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ failedCount }}</div>
                <div class="stats-label">失败登录</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon blocked">
                <el-icon><Lock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ blockedCount }}</div>
                <div class="stats-label">被阻止</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ totalCount }}</div>
                <div class="stats-label">总计</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 登录日志表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span class="card-title">登录日志列表</span>
          <span class="log-count">共 {{ monitoringStore.pagination.total }} 条记录</span>
        </div>
      </template>

      <el-table 
        :data="monitoringStore.loginLogs" 
        v-loading="monitoringStore.loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="username" label="用户名" width="120" />
        
        <el-table-column prop="loginResult" label="登录结果" width="100">
          <template #default="{ row }">
            <el-tag 
              :type="getResultTagType(row.loginResult)" 
              size="small"
            >
              {{ getResultText(row.loginResult) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="ipAddress" label="IP地址" width="140" />
        
        <el-table-column label="地理位置" width="200">
          <template #default="{ row }">
            <div class="location-info">
              <el-icon><Location /></el-icon>
              <span>{{ monitoringStore.formatLocation(row) }}</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="loginTime" label="登录时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.loginTime) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="logoutTime" label="登出时间" width="160">
          <template #default="{ row }">
            <span v-if="row.logoutTime">{{ formatDateTime(row.logoutTime) }}</span>
            <span v-else class="no-logout">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="会话时长" width="120">
          <template #default="{ row }">
            <span v-if="row.sessionDuration">
              {{ formatDuration(row.sessionDuration) }}
            </span>
            <span v-else class="no-duration">-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="失败原因" width="150">
          <template #default="{ row }">
            <span v-if="row.failureReason" class="failure-reason">
              {{ row.failureReason }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column label="设备信息" width="150">
          <template #default="{ row }">
            <div class="device-info">
              <div v-if="row.userAgent" class="user-agent">
                {{ formatUserAgent(row.userAgent) }}
              </div>
              <div v-if="row.deviceInfo" class="device-details">
                <el-tag size="small" v-if="row.deviceInfo.platform">
                  {{ row.deviceInfo.platform }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="viewLogDetails(row)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="monitoringStore.pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <LogDetailDialog 
      v-model="detailDialogVisible"
      :log="selectedLog"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, 
  Refresh, 
  Download, 
  Check, 
  Close, 
  Lock, 
  DataAnalysis, 
  Location 
} from '@element-plus/icons-vue'
import { useMonitoringStore } from '@/stores/monitoring'
import { formatDateTime, formatDuration } from '@/utils/date'
import type { LoginLog, LoginLogFilters, LoginResult } from '@/types'
import LogDetailDialog from '@/components/LogDetailDialog.vue'

const monitoringStore = useMonitoringStore()

// 筛选条件
const filters = reactive<LoginLogFilters>({
  username: '',
  ipAddress: '',
  loginResult: undefined,
  locationProvince: '',
  locationCity: '',
  startDate: '',
  endDate: '',
  page: 1,
  limit: 20
})

// 日期范围
const dateRange = ref<[string, string] | null>(null)

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 对话框状态
const detailDialogVisible = ref(false)
const selectedLog = ref<LoginLog | null>(null)

// 计算统计数据
const successCount = computed(() => 
  monitoringStore.loginLogs.filter(log => log.loginResult === 'SUCCESS').length
)

const failedCount = computed(() => 
  monitoringStore.loginLogs.filter(log => log.loginResult === 'FAILED').length
)

const blockedCount = computed(() => 
  monitoringStore.loginLogs.filter(log => log.loginResult === 'BLOCKED').length
)

const totalCount = computed(() => monitoringStore.loginLogs.length)

// 监听日期范围变化
watch(dateRange, (newRange) => {
  if (newRange) {
    filters.startDate = newRange[0]
    filters.endDate = newRange[1]
  } else {
    filters.startDate = ''
    filters.endDate = ''
  }
})

// 获取结果标签类型
const getResultTagType = (result: LoginResult) => {
  switch (result) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    case 'BLOCKED': return 'warning'
    default: return 'info'
  }
}

// 获取结果文本
const getResultText = (result: LoginResult) => {
  switch (result) {
    case 'SUCCESS': return '成功'
    case 'FAILED': return '失败'
    case 'BLOCKED': return '被阻止'
    default: return '未知'
  }
}

// 格式化用户代理
const formatUserAgent = (userAgent: string): string => {
  if (userAgent.includes('Chrome')) {
    return 'Chrome'
  } else if (userAgent.includes('Firefox')) {
    return 'Firefox'
  } else if (userAgent.includes('Safari')) {
    return 'Safari'
  } else if (userAgent.includes('Edge')) {
    return 'Edge'
  } else if (userAgent.includes('Test-Client')) {
    return '测试客户端'
  } else {
    return '其他'
  }
}

// 应用筛选
const applyFilters = () => {
  const cleanFilters = Object.fromEntries(
    Object.entries({
      ...filters,
      page: currentPage.value,
      limit: pageSize.value
    }).filter(([_, value]) => value !== '' && value !== undefined)
  )
  monitoringStore.fetchLoginLogs(cleanFilters)
}

// 重置筛选
const resetFilters = () => {
  Object.assign(filters, {
    username: '',
    ipAddress: '',
    loginResult: undefined,
    locationProvince: '',
    locationCity: '',
    startDate: '',
    endDate: '',
    page: 1,
    limit: 20
  })
  dateRange.value = null
  currentPage.value = 1
  pageSize.value = 20
  monitoringStore.fetchLoginLogs()
}

// 刷新数据
const refreshData = () => {
  applyFilters()
}

// 导出日志
const exportLogs = () => {
  ElMessage.info('导出功能开发中...')
}

// 查看日志详情
const viewLogDetails = (log: LoginLog) => {
  selectedLog.value = log
  detailDialogVisible.value = true
}

// 处理页面大小变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  applyFilters()
}

// 处理当前页变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  applyFilters()
}

// 初始化
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.login-logs {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;

    .header-content {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;
      }

      .page-description {
        color: #606266;
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  .filter-card {
    margin-bottom: 20px;

    .filter-content {
      .el-form {
        .el-form-item {
          margin-bottom: 0;
        }
      }
    }
  }

  .stats-cards {
    margin-bottom: 20px;

    .stats-card {
      .stats-content {
        display: flex;
        align-items: center;
        gap: 16px;

        .stats-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;

          &.success {
            background-color: #f0f9ff;
            color: #67c23a;
          }

          &.failed {
            background-color: #fef0f0;
            color: #f56c6c;
          }

          &.blocked {
            background-color: #fdf6ec;
            color: #e6a23c;
          }

          &.total {
            background-color: #f4f4f5;
            color: #909399;
          }
        }

        .stats-info {
          .stats-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
          }

          .stats-label {
            font-size: 14px;
            color: #606266;
          }
        }
      }
    }
  }

  .table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
      }

      .log-count {
        font-size: 14px;
        color: #606266;
      }
    }

    .location-info {
      display: flex;
      align-items: center;
      gap: 4px;

      .el-icon {
        color: #909399;
      }
    }

    .device-info {
      .user-agent {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .device-details {
        .el-tag {
          margin-right: 4px;
        }
      }
    }

    .failure-reason {
      color: #f56c6c;
      font-size: 12px;
    }

    .no-logout,
    .no-duration {
      color: #c0c4cc;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
