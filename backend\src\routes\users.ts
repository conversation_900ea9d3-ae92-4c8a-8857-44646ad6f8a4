import { Router } from 'express';
import {
  getUsers,
  getUserById,
  updateUser,
  deleteUser,
  getEmployeeStats,
  getMyStats,
} from '../controllers/userController';
import { authenticateToken, requireBossOrAdmin, requireOwnershipOrAdmin } from '../middleware/auth';
import { validateQuery, validateParams, validate } from '../utils/validation';
import { commonSchemas } from '../utils/validation';
import Joi from 'joi';

const router = Router();

// 用户更新验证schema
const updateUserSchema = Joi.object({
  nickname: Joi.string().max(50).optional(),
  phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional(),
  level: Joi.number().integer().min(1).max(10).optional(),
  status: Joi.string().valid('ACTIVE', 'INACTIVE', 'BANNED').optional(),
});

// 查询参数验证schema
const userQuerySchema = Joi.object({
  page: Joi.alternatives().try(
    Joi.number().integer().min(1),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(1),
  limit: Joi.alternatives().try(
    Joi.number().integer().min(1).max(100),
    Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
  ).default(10),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  role: Joi.string().valid('ADMIN', 'BOSS', 'EMPLOYEE').optional(),
  status: Joi.string().valid('ACTIVE', 'INACTIVE', 'BANNED').optional(),
  keyword: Joi.string().allow('').optional(),
});

// 获取用户列表（需要老板或管理员权限）
router.get('/', 
  authenticateToken, 
  requireBossOrAdmin, 
  validateQuery(userQuerySchema), 
  getUsers
);

// 获取当前用户的统计信息
router.get('/me/stats', authenticateToken, getMyStats);

// 获取用户详情（需要权限检查）
router.get('/:id', 
  authenticateToken, 
  validateParams(Joi.object({ id: commonSchemas.id })),
  requireOwnershipOrAdmin('id'),
  getUserById
);

// 更新用户信息（需要权限检查）
router.put('/:id', 
  authenticateToken, 
  validateParams(Joi.object({ id: commonSchemas.id })),
  validate(updateUserSchema),
  requireOwnershipOrAdmin('id'),
  updateUser
);

// 删除用户（需要管理员权限）
router.delete('/:id', 
  authenticateToken, 
  requireBossOrAdmin,
  validateParams(Joi.object({ id: commonSchemas.id })),
  deleteUser
);

// 获取员工统计信息（需要老板或管理员权限）
router.get('/:id/stats', 
  authenticateToken, 
  requireBossOrAdmin,
  validateParams(Joi.object({ id: commonSchemas.id })),
  getEmployeeStats
);

export default router;
