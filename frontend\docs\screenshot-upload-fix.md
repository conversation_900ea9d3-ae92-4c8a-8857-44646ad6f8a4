# 任务进度截图上传功能修复报告

## 问题描述

员工端任务进度截图上传功能存在以下问题：

1. **ElTag组件type属性错误**：`SUBMITTED`状态的type值为空字符串，导致Element Plus组件验证失败
2. **ElImage组件previewSrcList属性错误**：后端返回的screenshots是JSON字符串，前端期望数组格式
3. **截图上传成功但显示"加载失败"**：数据格式处理不一致导致前端无法正确显示截图
4. **老板端和管理员端无法查看截图**：后端返回数据格式问题

## 修复内容

### 1. 修复TaskDetailDialog.vue中的ElTag组件问题

**文件**: `frontend/src/components/TaskDetailDialog.vue`

**修复内容**:
- 将`getStatusType`函数中`SUBMITTED`状态的type从空字符串改为`'warning'`
- 添加TypeScript类型约束，确保返回值符合Element Plus要求的类型

```typescript
const getStatusType = (status: TaskStatus) => {
  const statusMap: Record<TaskStatus, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    PENDING: 'warning',
    ACCEPTED: 'info',
    IN_PROGRESS: 'primary',
    PAUSED: 'info',
    SUBMITTED: 'warning', // 修复：从空字符串改为'warning'
    APPROVED: 'success',
    REJECTED: 'danger',
    COMPLETED: 'success',
    CANCELLED: 'info'
  }
  return statusMap[status] || 'info'
}
```

### 2. 修复ElImage组件的previewSrcList属性问题

**文件**: `frontend/src/components/TaskDetailDialog.vue`

**修复内容**:
- 添加`getScreenshots`辅助函数，处理字符串格式的截图数组
- 支持JSON字符串解析和手动字符串分割两种方式

```typescript
// 获取截图数组（处理字符串格式的数组）
const getScreenshots = (screenshots: any): string[] => {
  if (!screenshots) return []
  
  // 如果是字符串，尝试解析为数组
  if (typeof screenshots === 'string') {
    try {
      // 处理类似 "["/uploads/screenshots/xxx.jpg"]" 的字符串
      const parsed = JSON.parse(screenshots)
      return Array.isArray(parsed) ? parsed : [screenshots]
    } catch {
      // 如果解析失败，检查是否是单个URL
      return screenshots.startsWith('[') && screenshots.endsWith(']') 
        ? screenshots.slice(1, -1).split(',').map(s => s.trim().replace(/"/g, ''))
        : [screenshots]
    }
  }
  
  // 如果已经是数组，直接返回
  if (Array.isArray(screenshots)) {
    return screenshots
  }
  
  return []
}
```

### 3. 修复MyTasks.vue中的相同问题

**文件**: `frontend/src/views/employee/MyTasks.vue`

**修复内容**:
- 同样修复`getStatusType`函数中的`SUBMITTED`状态type值问题

### 4. 修复后端数据格式问题

**文件**: `backend/src/services/taskService.ts`

**修复内容**:
- 在`getTaskById`方法中添加数据格式化逻辑
- 在`getTasks`方法中添加数据格式化逻辑
- 在`getAvailableTasks`方法中处理progress字段

```typescript
// 格式化进度数据中的screenshots字段
const formattedTask = {
  ...task,
  progress: task.progress.map((p: any) => ({
    ...p,
    screenshots: p.screenshots ? JSON.parse(p.screenshots) : []
  }))
};
```

## 技术细节

### 数据流程
1. **上传阶段**: 前端通过Element Plus Upload组件上传截图到`/api/v1/upload/screenshots`
2. **存储阶段**: 后端将截图URL数组通过`JSON.stringify`存储到数据库
3. **读取阶段**: 后端查询时通过`JSON.parse`将字符串转换回数组格式
4. **显示阶段**: 前端接收到正确的数组格式，Element Plus组件正常显示

### 类型安全
- 为`getStatusType`函数添加了严格的TypeScript类型约束
- 使用`Record<TaskStatus, ValidTagType>`确保返回值类型安全
- 添加`(p: any)`类型注解解决Prisma类型推断问题

## 测试验证

修复完成后，请验证以下功能：

1. **员工端任务详情页面**:
   - 任务状态标签显示正常，无控制台错误
   - 进度记录中的截图能正常显示和预览
   - 截图预览列表功能正常

2. **员工端任务列表页面**:
   - 任务状态标签显示正常
   - 无Vue组件属性验证错误

3. **进度更新功能**:
   - 截图上传成功后能正常显示
   - 提交进度后截图数据正确保存

4. **老板端和管理员端**:
   - 能正常查看员工上传的进度截图
   - 任务详情页面截图显示正常

## 注意事项

1. 此修复保持了向后兼容性，支持已有的字符串格式数据
2. 新上传的截图将以正确的数组格式存储和返回
3. 所有修改都添加了适当的错误处理和类型检查
4. 修复不影响其他功能模块的正常运行

## 相关文件

- `frontend/src/components/TaskDetailDialog.vue`
- `frontend/src/views/employee/MyTasks.vue`
- `backend/src/services/taskService.ts`
- `frontend/src/components/UpdateProgressDialog.vue`（无需修改，逻辑正确）
