/**
 * 统一的导航样式系统
 */

// 导入sass:map模块以使用map.get函数
@use 'sass:map';

// 导航主题色彩
$nav-themes: (
  boss: (
    primary: #1f2937,
    secondary: #374151,
    accent: #3b82f6,
    text: #f9fafb,
    text-secondary: #d1d5db,
    active: #3b82f6,
    hover: #4b5563
  ),
  employee: (
    primary: #065f46,
    secondary: #047857,
    accent: #10b981,
    text: #f0fdf4,
    text-secondary: #bbf7d0,
    active: #10b981,
    hover: #059669
  ),
  admin: (
    primary: #7c2d12,
    secondary: #9a3412,
    accent: #ea580c,
    text: #fef7ff,
    text-secondary: #fed7aa,
    active: #ea580c,
    hover: #c2410c
  )
);

// 导航尺寸
$nav-sidebar-width: 260px;
$nav-sidebar-collapsed-width: 64px;
$nav-header-height: 64px;
$nav-logo-height: 64px;

// 导航动画
$nav-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

// 基础导航布局
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

// 侧边栏基础样式
.nav-sidebar {
  width: $nav-sidebar-width;
  height: 100vh;
  position: relative;
  transition: $nav-transition;
  overflow: hidden;
  z-index: 100;

  &.collapsed {
    width: $nav-sidebar-collapsed-width;
  }

  // 主题样式混入
  @each $theme, $colors in $nav-themes {
    &.theme-#{$theme} {
      background: map.get($colors, primary);
      color: map.get($colors, text);

      .nav-logo {
        background: map.get($colors, secondary);
        color: map.get($colors, text);
      }

      .nav-menu {
        background: map.get($colors, primary);

        .nav-menu-item {
          color: map.get($colors, text-secondary);

          &:hover {
            background: map.get($colors, hover);
            color: map.get($colors, text);
          }

          &.is-active {
            background: map.get($colors, active);
            color: white;
            box-shadow: inset 3px 0 0 white;
          }
        }

        .nav-submenu {
          .nav-submenu-title {
            color: map.get($colors, text-secondary);

            &:hover {
              background: map.get($colors, hover);
              color: map.get($colors, text);
            }
          }

          &.is-opened .nav-submenu-title {
            background: map.get($colors, secondary);
            color: map.get($colors, text);
          }
        }
      }
    }
  }
}

// Logo区域
.nav-logo {
  height: $nav-logo-height;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: $nav-transition;

  .logo-text {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    transition: $nav-transition;
    white-space: nowrap;

    &.collapsed {
      font-size: 16px;
    }
  }

  .logo-icon {
    font-size: 24px;
    transition: $nav-transition;
  }
}

// 菜单样式
.nav-menu {
  border: none;
  width: 100%;
  background: transparent;

  .nav-menu-item {
    height: 56px;
    line-height: 56px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: $nav-transition;
    position: relative;
    border: none;
    margin: 0;

    .nav-icon {
      width: 20px;
      height: 20px;
      margin-right: 12px;
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: $nav-transition;
    }

    .nav-text {
      flex: 1;
      transition: $nav-transition;
      white-space: nowrap;
      overflow: hidden;
    }

    &.collapsed {
      padding: 0 22px;
      justify-content: center;

      .nav-icon {
        margin-right: 0;
      }

      .nav-text {
        display: none;
      }
    }
  }

  .nav-submenu {
    .nav-submenu-title {
      height: 56px;
      line-height: 56px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: $nav-transition;
      position: relative;

      .nav-icon {
        width: 20px;
        height: 20px;
        margin-right: 12px;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: $nav-transition;
      }

      .nav-text {
        flex: 1;
        transition: $nav-transition;
        white-space: nowrap;
        overflow: hidden;
      }

      .nav-arrow {
        width: 16px;
        height: 16px;
        transition: transform 0.3s ease;

        &.expanded {
          transform: rotate(90deg);
        }
      }

      // 收缩状态样式
      &.collapsed {
        padding: 0 22px;
        justify-content: center;

        .nav-icon {
          margin-right: 0;
        }

        .nav-text {
          display: none;
        }

        .nav-arrow {
          display: none;
        }
      }
    }

    .nav-submenu-items {
      background: rgba(0, 0, 0, 0.1);
      overflow: hidden;
      transition: max-height 0.3s ease;

      .nav-menu-item {
        padding-left: 52px;
        height: 48px;
        line-height: 48px;
        font-size: 14px;
      }
    }
  }
}

// 顶部导航栏
.nav-header {
  height: $nav-header-height;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  position: relative;
  z-index: 50;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .nav-toggle {
      width: 40px;
      height: 40px;
      border: none;
      background: transparent;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: $nav-transition;
      color: #6b7280;

      &:hover {
        background: #f3f4f6;
        color: #374151;
      }

      .toggle-icon {
        width: 20px;
        height: 20px;
        transition: $nav-transition;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .user-dropdown {
      .user-trigger {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        transition: $nav-transition;
        color: #374151;

        &:hover {
          background: #f3f4f6;
        }

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #3b82f6;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 14px;
        }

        .user-name {
          font-weight: 500;
          font-size: 14px;
        }

        .dropdown-arrow {
          width: 16px;
          height: 16px;
          transition: transform 0.3s ease;
        }
      }
    }
  }
}

// 主内容区域
.nav-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .main-content {
    flex: 1;
    background: #f8fafc;
    padding: 24px;
    overflow-y: auto;
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .nav-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;

    &.mobile-open {
      transform: translateX(0);
    }
  }

  .nav-main {
    margin-left: 0;
  }

  .nav-header {
    padding: 0 16px;
  }

  .main-content {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .nav-header {
    .header-right {
      gap: 8px;

      .user-dropdown .user-trigger {
        padding: 6px 8px;

        .user-name {
          display: none;
        }
      }
    }
  }

  .main-content {
    padding: 12px;
  }
}

// 导航遮罩层（移动端）
.nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.visible {
    opacity: 1;
    visibility: visible;
  }
}

// 滚动条样式
.nav-menu {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// Tooltip样式（收缩状态下的提示）
.nav-menu-item.collapsed,
.nav-submenu.collapsed .nav-submenu-title {
  position: relative;

  &:hover::after {
    content: attr(title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    margin-left: 12px;
    pointer-events: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: tooltipFadeIn 0.2s ease-out;
  }

  &:hover::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 6px solid transparent;
    border-right-color: rgba(0, 0, 0, 0.9);
    margin-left: 6px;
    z-index: 1000;
    pointer-events: none;
    animation: tooltipFadeIn 0.2s ease-out;
  }
}

// Tooltip动画
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}
