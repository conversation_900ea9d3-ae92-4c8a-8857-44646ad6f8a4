<template>
  <div class="available-tasks-container">
    <div class="page-header">
      <h1>可接单任务</h1>
      <div class="header-actions">
        <el-button @click="fetchAvailableTasks" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 游戏类型筛选 -->
    <el-card class="filter-card" shadow="never">
      <div class="game-type-filters">
        <div class="filter-label">游戏类型：</div>
        <div class="filter-buttons">
          <el-button
            :type="selectedGameType === '' ? 'primary' : 'default'"
            @click="handleGameTypeChange('')"
            size="small"
          >
            全部 ({{ totalTaskCount }})
          </el-button>
          <el-button
            v-for="gameType in availableGameTypes"
            :key="gameType.value"
            :type="selectedGameType === gameType.value ? 'primary' : 'default'"
            @click="handleGameTypeChange(gameType.value)"
            size="small"
          >
            {{ gameType.label }} ({{ getGameTypeTaskCount(gameType.value) }})
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 任务列表 -->
    <div class="tasks-container">
      <!-- 当选择了特定游戏类型时显示游戏模块 -->
      <div v-if="selectedGameType" class="game-module">
        <div class="game-module-header">
          <div class="game-info">
            <el-icon class="game-icon"><Trophy /></el-icon>
            <h2>{{ getGameTypeName(selectedGameType) }}</h2>
            <el-tag type="info" size="small">{{ filteredTasks.length }}个任务</el-tag>
          </div>
        </div>

        <!-- 按优先级分组显示 -->
        <div
          v-for="group in groupedTasks"
          :key="group.priority"
          class="priority-group"
        >
          <div class="group-header">
            <el-tag
              :type="getPriorityType(group.priority)"
              size="large"
              class="priority-tag"
            >
              {{ getPriorityText(group.priority) }}任务
            </el-tag>
            <span class="task-count">{{ group.tasks.length }}个</span>
          </div>

          <div class="tasks-grid">
            <el-card
              v-for="task in group.tasks"
              :key="task.id"
              class="task-card"
              shadow="hover"
            >
              <template #header>
                <div class="task-header">
                  <span class="task-no">{{ task.taskNo }}</span>
                  <span class="created-time">{{ formatDate(task.createdAt) }}</span>
                </div>
              </template>

              <div class="task-content">
                <div class="order-info">
                  <h3>{{ task.order?.customerName }}</h3>
                  <div class="game-info" v-if="!selectedGameType && task.order?.gameType">
                    <el-tag type="primary" size="small">
                      {{ getGameTypeName(task.order.gameType) }}
                    </el-tag>
                  </div>

                </div>

                <div class="task-details">
                  <div class="detail-item">
                    <span class="label">订单价格:</span>
                    <span class="value">¥{{ task.order?.price }}</span>
                  </div>
                  <div class="detail-item" v-if="task.commission">
                    <span class="label">佣金:</span>
                    <span class="value commission">¥{{ task.commission }}</span>
                  </div>
                  <div class="detail-item" v-if="task.estimatedHours">
                    <span class="label">预计工时:</span>
                    <span class="value">{{ task.estimatedHours }}小时</span>
                  </div>
                  <div class="detail-item" v-if="task.order?.deadline">
                    <span class="label">截止时间:</span>
                    <span class="value">{{ formatDate(task.order.deadline) }}</span>
                  </div>
                </div>

                <div class="task-description" v-if="task.description">
                  <p>{{ task.description }}</p>
                </div>
              </div>

              <template #footer>
                <div class="task-actions">
                  <el-button
                    type="primary"
                    @click="handleAcceptTask(task)"
                    :loading="acceptingTaskId === task.id"
                  >
                    接单
                  </el-button>
                  <el-button @click="handleViewDetails(task)">
                    查看详情
                  </el-button>
                </div>
              </template>
            </el-card>
          </div>
        </div>
      </div>

      <!-- 当未选择游戏类型时显示所有游戏的任务 -->
      <div v-else>
        <!-- 按优先级分组显示 -->
        <div
          v-for="group in groupedTasks"
          :key="group.priority"
          class="priority-group"
        >
          <div class="group-header">
            <el-tag
              :type="getPriorityType(group.priority)"
              size="large"
              class="priority-tag"
            >
              {{ getPriorityText(group.priority) }}任务
            </el-tag>
            <span class="task-count">{{ group.tasks.length }}个</span>
          </div>

          <div class="tasks-grid">
            <el-card
              v-for="task in group.tasks"
              :key="task.id"
              class="task-card"
              shadow="hover"
            >
              <template #header>
                <div class="task-header">
                  <span class="task-no">{{ task.taskNo }}</span>
                  <span class="created-time">{{ formatDate(task.createdAt) }}</span>
                </div>
              </template>

              <div class="task-content">
                <div class="order-info">
                  <h3>{{ task.order?.customerName }}</h3>
                  <div class="game-info">
                    <el-tag type="primary" size="small" v-if="task.order?.gameType">
                      {{ getGameTypeName(task.order.gameType) }}
                    </el-tag>
                  </div>

                </div>

                <div class="task-details">
                  <div class="detail-item">
                    <span class="label">订单价格:</span>
                    <span class="value">¥{{ task.order?.price }}</span>
                  </div>
                  <div class="detail-item" v-if="task.commission">
                    <span class="label">佣金:</span>
                    <span class="value commission">¥{{ task.commission }}</span>
                  </div>
                  <div class="detail-item" v-if="task.estimatedHours">
                    <span class="label">预计工时:</span>
                    <span class="value">{{ task.estimatedHours }}小时</span>
                  </div>
                  <div class="detail-item" v-if="task.order?.deadline">
                    <span class="label">截止时间:</span>
                    <span class="value">{{ formatDate(task.order.deadline) }}</span>
                  </div>
                </div>

                <div class="task-description" v-if="task.description">
                  <p>{{ task.description }}</p>
                </div>
              </div>

              <template #footer>
                <div class="task-actions">
                  <el-button
                    type="primary"
                    @click="handleAcceptTask(task)"
                    :loading="acceptingTaskId === task.id"
                  >
                    接单
                  </el-button>
                  <el-button @click="handleViewDetails(task)">
                    查看详情
                  </el-button>
                </div>
              </template>
            </el-card>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <el-empty
      v-if="!loading && availableTasks.length === 0"
      description="暂无可接单任务"
    />

    <!-- 分页 -->
    <div class="pagination-container" v-if="availableTasks.length > 0">
      <el-pagination
        :current-page="pagination.page"
        :page-size="pagination.limit"
        :total="pagination.total"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="showDetailDialog"
      :task-id="selectedTaskId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Trophy } from '@element-plus/icons-vue'
import { taskApi } from '../../api/tasks'
import type { Task, OrderPriority } from '../../types/index'
import { formatDate } from '../../utils/date'
// 移除已删除的模板系统API导入
import TaskDetailDialog from '../../components/TaskDetailDialog.vue'

// 响应式数据
const loading = ref(false)
const availableTasks = ref<Task[]>([])
const acceptingTaskId = ref<string | null>(null)
const showDetailDialog = ref(false)
const selectedTaskId = ref<string | null>(null)

// 游戏类型筛选相关
const selectedGameType = ref<string>('')
const availableGameTypes = ref<Array<{ value: string; label: string }>>([])
const gameTypesLoading = ref(false)

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

// 计算属性：筛选后的任务
const filteredTasks = computed(() => {
  if (!selectedGameType.value) {
    return availableTasks.value
  }
  return availableTasks.value.filter(task =>
    task.order?.gameType === selectedGameType.value
  )
})

// 计算属性：按优先级分组任务
const groupedTasks = computed(() => {
  const priorityOrder: OrderPriority[] = ['URGENT', 'HIGH', 'NORMAL', 'LOW']
  const groups: { priority: OrderPriority; tasks: Task[] }[] = []

  // 按优先级分组
  priorityOrder.forEach(priority => {
    const tasks = filteredTasks.value.filter(task => task.order?.priority === priority)
    if (tasks.length > 0) {
      groups.push({ priority, tasks })
    }
  })

  return groups
})

// 总任务数量
const totalTaskCount = computed(() => availableTasks.value.length)

// 获取指定游戏类型的任务数量
const getGameTypeTaskCount = (gameType: string) => {
  return availableTasks.value.filter(task =>
    task.order?.gameType === gameType
  ).length
}

// 获取游戏类型列表
const fetchGameTypes = async () => {
  try {
    gameTypesLoading.value = true
    // 使用默认游戏类型（模板系统已删除）
    availableGameTypes.value = [
      { value: 'wzry', label: '王者荣耀' },
      { value: 'lol', label: '英雄联盟' },
      { value: 'ys', label: '原神' },
      { value: 'mc', label: '鸣潮' }
    ]
  } finally {
    gameTypesLoading.value = false
  }
}

// 获取可接单任务列表
const fetchAvailableTasks = async () => {
  try {
    loading.value = true
    const response = await taskApi.getAvailableTasks({
      page: pagination.page,
      limit: pagination.limit
    })

    if (response.success && response.data) {
      availableTasks.value = response.data.items
      pagination.total = response.data.pagination.total
    }
  } catch (error) {
    console.error('获取可接单任务失败:', error)
    ElMessage.error('获取可接单任务失败')
  } finally {
    loading.value = false
  }
}

// 处理游戏类型切换
const handleGameTypeChange = (gameType: string) => {
  selectedGameType.value = gameType
  // 重置分页
  pagination.page = 1
}

// 接单
const handleAcceptTask = async (task: Task) => {
  try {
    await ElMessageBox.confirm(
      `确定要接受任务 ${task.taskNo} 吗？`,
      '确认接单',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    acceptingTaskId.value = task.id
    const response = await taskApi.acceptTask(task.id)

    if (response.success) {
      ElMessage.success('接单成功')
      fetchAvailableTasks() // 刷新列表
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('接单失败:', error)
      ElMessage.error('接单失败')
    }
  } finally {
    acceptingTaskId.value = null
  }
}

// 查看详情
const handleViewDetails = (task: Task) => {
  selectedTaskId.value = task.id
  showDetailDialog.value = true
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchAvailableTasks()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchAvailableTasks()
}

// 优先级相关方法
const getPriorityType = (priority?: OrderPriority) => {
  if (!priority) return 'primary'
  const priorityMap = {
    LOW: 'info',
    NORMAL: 'primary',
    HIGH: 'warning',
    URGENT: 'danger'
  }
  return priorityMap[priority] || 'primary'
}

const getPriorityText = (priority?: OrderPriority) => {
  if (!priority) return '普通'
  const priorityMap = {
    LOW: '低',
    NORMAL: '普通',
    HIGH: '高',
    URGENT: '紧急'
  }
  return priorityMap[priority] || priority
}

// 游戏类型名称映射
const gameTypeNames: Record<string, string> = {
  'wzry': '王者荣耀',
  'lol': '英雄联盟',
  'ys': '原神',
  'hpjy': '和平精英',
  'pubg': '绝地求生',
  'mc': '鸣潮',
  'csgo': 'CSGO',
  'dota2': 'DOTA2'
}

const getGameTypeName = (gameType: string): string => {
  return gameTypeNames[gameType] || gameType
}

// 初始化
onMounted(async () => {
  await Promise.all([
    fetchGameTypes(),
    fetchAvailableTasks()
  ])
})
</script>

<style lang="scss" scoped>
.available-tasks-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    color: var(--text-primary);
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.filter-card {
  margin-bottom: 20px;

  .game-type-filters {
    display: flex;
    align-items: center;
    gap: 16px;

    .filter-label {
      font-weight: 500;
      color: var(--el-text-color-primary);
      white-space: nowrap;
    }

    .filter-buttons {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }
}

.game-module {
  .game-module-header {
    margin-bottom: 20px;

    .game-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .game-icon {
        font-size: 24px;
        color: var(--el-color-primary);
      }

      h2 {
        margin: 0;
        color: var(--el-text-color-primary);
        font-size: 20px;
      }
    }
  }
}

.tasks-container {
  margin-bottom: 20px;
}

.priority-group {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }

  .group-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f2f5;

    .priority-tag {
      font-weight: bold;
      font-size: 16px;
      padding: 8px 16px;
    }

    .task-count {
      color: #909399;
      font-size: 14px;
      background: #f5f7fa;
      padding: 4px 8px;
      border-radius: 12px;
    }
  }
}

.tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.task-card {
  .task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .task-no {
      font-weight: bold;
      color: var(--text-primary);
    }

    .created-time {
      font-size: 12px;
      color: #909399;
    }
  }

  .task-content {
    .order-info {
      margin-bottom: 16px;

      h3 {
        margin: 0 0 8px 0;
        color: var(--text-primary);
      }


    }

    .task-details {
      margin-bottom: 16px;

      .detail-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        .label {
          color: #909399;
          font-size: 14px;
        }

        .value {
          color: var(--text-primary);
          font-weight: 500;

          &.commission {
            color: #f56c6c;
            font-weight: bold;
          }
        }
      }
    }

    .task-description {
      padding: 12px;
      background-color: #f5f7fa;
      border-radius: 4px;
      margin-bottom: 16px;

      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }

  .task-actions {
    display: flex;
    gap: 8px;
  }
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}
</style>
