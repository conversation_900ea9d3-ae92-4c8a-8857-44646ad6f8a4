### 模块二：【老板端】订单创建模块
老板在这里使用他设计好的“图纸”来“生产”订单。
工作流程与前端实现
1、选择游戏:
页面顶部是一个下拉框 [请选择游戏]，数据源是后台所有“上架”状态的游戏。
2、动态生成表单:
当老板选择了某个游戏（如《王者荣耀》）后，前端立即触发一个API请求：GET /api/games/123/form-fields (123是王者荣耀的ID)。
API返回一个JSON数组，包含了为《王者荣耀》配置的所有字段信息（就是模块一里配置的那些）。
前端拿到这个JSON数组后，遍历数组，根据每个对象的 field_type 来动态创建对应的HTML控件。
if (field.type === 'text') { render <input type="text"> }
if (field.type === 'select') { render <select> and loop through field.options to render <option> }
...以此类推。
同时，将 field_label, is_required, placeholder 等属性应用到控件上。
3、提交表单:
老板填写完所有信息后，点击“提交”。
前端将表单数据收集成一个键值对的JSON对象。键就是配置的 field_key，值就是用户输入的内容。
示例数据 (Payload):
Generated json
{
  "game_id": 123,
  "employee_id": 45, // 分配的员工ID
  "total_price": 200, // 订单总价
  "form_data": {
    "server_region": "QQ安卓",
    "current_rank": "钻石V",
    "target_rank": "星耀V",
    "account_name": "user_abc",
    "password": "Password123!",
    "notes": "晚上10点后请勿登录"
  }
}
Use code with caution.
Json
后端数据存储
后端API接收到这个Payload。
将 game_id, employee_id, total_price 等通用信息存入 orders 表的对应字段。
将 form_data 这个子JSON对象，整体作为一个字符串或JSON类型，存入 orders 表的 details 字段。
数据库表结构（核心）:
orders 表:
id (INT)
game_id (INT, 外键)
employee_id (INT, 外键)
details (JSON 或 TEXT 类型) - 这就是灵活性的核心！
...其他通用字段