import { OrderStatus, OrderPriority } from '@prisma/client';
import { prisma } from '../config/database';
import { NotFoundError, AuthorizationError, ValidationError } from '../middleware/errorHandler';
import { PaginationQuery, PaginatedResponse } from '../types/common';

export interface CreateOrderData {
  customerName: string;
  customerContact?: string;
  gameAccount: string;
  gamePassword: string;
  gameType: string;
  currentRank?: string;
  targetRank?: string;
  price: number;
  deadline?: Date;
  requirements?: string;
  priority?: OrderPriority;
  // 动态表单驱动系统字段
  details?: Record<string, any>;  // 核心：存储动态表单数据
  formData?: Record<string, any>; // 兼容字段
  // 模板相关字段（保留兼容性）
  templateId?: string;
  templateVersion?: number;
}

export interface UpdateOrderData {
  customerName?: string;
  customerContact?: string;
  gameType?: string;
  currentRank?: string;
  targetRank?: string;
  price?: number;
  deadline?: Date;
  requirements?: string;
  priority?: OrderPriority;
  status?: OrderStatus;
}

export class OrderService {
  // 生成订单编号
  private generateOrderNo(): string {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD${timestamp.slice(-6)}${random}`;
  }

  // 创建订单
  async createOrder(createdById: string, orderData: CreateOrderData) {
    const orderNo = this.generateOrderNo();

    // 基本数据验证 - 只验证通用必填字段，游戏特定字段已在中间件中验证
    if (orderData.price <= 0) {
      throw new ValidationError('订单价格必须大于0');
    }

    // 如果提供了模板ID，验证模板是否存在
    let template = null;
    if (orderData.templateId) {
      template = await prisma.orderTemplate.findUnique({
        where: { id: orderData.templateId }
      });
      if (!template) {
        throw new ValidationError('指定的订单模板不存在');
      }
    }

    const order = await prisma.order.create({
      data: {
        orderNo,
        customerName: orderData.customerName,
        customerContact: orderData.customerContact,
        gameAccount: orderData.gameAccount,
        gamePassword: orderData.gamePassword,
        gameType: orderData.gameType,
        currentRank: orderData.currentRank || null,
        targetRank: orderData.targetRank || null,
        price: orderData.price,
        deadline: orderData.deadline,
        requirements: orderData.requirements,
        priority: orderData.priority || OrderPriority.NORMAL,
        // 动态表单驱动系统核心实现
        details: orderData.details || orderData.formData || undefined,  // 优先使用details，兼容formData
        formData: orderData.formData || undefined,  // 保持向后兼容
        // 模板相关字段（保留兼容性）
        templateId: orderData.templateId || undefined,
        templateVersion: orderData.templateVersion || undefined,
        createdById,
        status: OrderStatus.PENDING,
      },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            nickname: true,
          },
        },
        template: {
          select: {
            id: true,
            name: true,
            gameType: true,
            fields: true,
            displayConfig: true,
          },
        },
      },
    });

    return order;
  }

  // 获取订单列表
  async getOrders(query: PaginationQuery & {
    status?: OrderStatus;
    priority?: OrderPriority;
    keyword?: string;
    createdById?: string;
    gameType?: string;
  }) {
    const {
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      status,
      priority,
      keyword,
      createdById,
      gameType
    } = query;
    
    const skip = (page - 1) * limit;
    
    // 构建查询条件
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (priority) {
      where.priority = priority;
    }
    
    if (createdById) {
      where.createdById = createdById;
    }

    if (gameType) {
      where.gameType = gameType;
    }

    if (keyword) {
      where.OR = [
        { orderNo: { contains: keyword } },
        { customerName: { contains: keyword } },
        { gameAccount: { contains: keyword } },
        { gameType: { contains: keyword } },
        { currentRank: { contains: keyword } },
        { targetRank: { contains: keyword } },
      ];
    }
    
    // 查询订单
    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          createdBy: {
            select: {
              id: true,
              username: true,
              nickname: true,
            },
          },
          tasks: {
            select: {
              id: true,
              taskNo: true,
              status: true,
              assignee: {
                select: {
                  id: true,
                  username: true,
                  nickname: true,
                },
              },
            },
          },
          _count: {
            select: {
              tasks: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder,
        },
      }),
      prisma.order.count({ where }),
    ]);
    
    const totalPages = Math.ceil(total / limit);
    
    const result: PaginatedResponse<typeof orders[0]> = {
      items: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
    
    return result;
  }

  // 获取订单详情
  async getOrderById(id: string) {
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            nickname: true,
          },
        },
        tasks: {
          include: {
            assignee: {
              select: {
                id: true,
                username: true,
                nickname: true,
                level: true,
              },
            },
            progress: {
              orderBy: {
                createdAt: 'desc',
              },
              take: 5,
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    if (!order) {
      throw new NotFoundError('订单不存在');
    }

    return order;
  }

  // 更新订单
  async updateOrder(id: string, updateData: UpdateOrderData, userId: string, userRole: string) {
    // 检查订单是否存在
    const existingOrder = await prisma.order.findUnique({
      where: { id },
      select: {
        id: true,
        createdById: true,
        status: true,
      },
    });

    if (!existingOrder) {
      throw new NotFoundError('订单不存在');
    }

    // 检查权限：管理员和老板可以修改任何订单，员工只能修改自己创建的订单
    const isAdmin = userRole === 'ADMIN';
    const isBoss = userRole === 'BOSS';
    const isOwner = existingOrder.createdById === userId;

    if (!isAdmin && !isBoss && !isOwner) {
      throw new AuthorizationError('只能修改自己创建的订单');
    }

    // 检查订单状态（已完成或已取消的订单不能修改）
    if (existingOrder.status === OrderStatus.COMPLETED || existingOrder.status === OrderStatus.CANCELLED) {
      throw new AuthorizationError('已完成或已取消的订单不能修改');
    }

    // 更新订单
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: updateData,
      include: {
        createdBy: {
          select: {
            id: true,
            username: true,
            nickname: true,
          },
        },
        tasks: {
          select: {
            id: true,
            taskNo: true,
            status: true,
          },
        },
      },
    });

    return updatedOrder;
  }

  // 删除订单
  async deleteOrder(id: string, userId: string, userRole: string) {
    // 检查订单是否存在
    const existingOrder = await prisma.order.findUnique({
      where: { id },
      include: {
        tasks: {
          select: {
            id: true,
            status: true,
            taskNo: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            username: true,
            role: true,
          },
        },
      },
    });

    if (!existingOrder) {
      throw new NotFoundError('订单不存在');
    }

    // 权限检查：管理员和老板可以删除任何订单，员工只能删除自己创建的订单
    const isAdmin = userRole === 'ADMIN';
    const isBoss = userRole === 'BOSS';
    const isOwner = existingOrder.createdById === userId;

    // 管理员和老板拥有最高权限，可以删除任何订单
    // 员工只能删除自己创建的订单
    if (!isAdmin && !isBoss && !isOwner) {
      throw new AuthorizationError('只能删除自己创建的订单');
    }

    // 订单状态检查：管理员和老板可以删除任何状态的订单，员工不能删除进行中的订单
    if (!isAdmin && !isBoss && existingOrder.status === OrderStatus.IN_PROGRESS) {
      throw new AuthorizationError('进行中的订单无法删除，请先取消订单');
    }

    // 关联任务检查：管理员和老板可以强制删除任何订单（即使有关联数据），员工需要先处理关联数据
    const activeTasks = existingOrder.tasks.filter(task =>
      task.status === 'ACCEPTED' || task.status === 'IN_PROGRESS'
    );

    if (activeTasks.length > 0) {
      if (isAdmin) {
        // 管理员可以强制删除，会在事务中处理关联数据
        console.log(`管理员 ${userId} 强制删除订单 ${id}，包含 ${activeTasks.length} 个进行中的任务`);
      } else if (isBoss) {
        // 老板可以删除任何订单，包含关联数据
        console.log(`老板 ${userId} 删除订单 ${id}，包含 ${activeTasks.length} 个进行中的任务`);
      } else {
        // 员工必须先处理关联数据
        const taskNos = activeTasks.map(task => task.taskNo).join(', ');
        throw new AuthorizationError(`订单存在关联数据，无法删除。请先处理相关任务或联系管理员。进行中的任务：${taskNos}`);
      }
    }

    try {
      // 使用事务删除订单及其关联数据
      await prisma.$transaction(async (tx) => {
        // 根据用户角色决定删除策略
        if (isAdmin || isBoss) {
          // 管理员或老板可以强制删除所有关联数据
          if (existingOrder.tasks.length > 0) {
            // 先删除任务进度记录
            await tx.taskProgress.deleteMany({
              where: {
                task: {
                  orderId: id,
                },
              },
            });

            // 删除结算记录
            await tx.settlement.deleteMany({
              where: {
                task: {
                  orderId: id,
                },
              },
            });

            // 删除所有任务（包括进行中的）
            await tx.task.deleteMany({
              where: {
                orderId: id,
              },
            });
          }
        } else {
          // 普通用户只能删除已完成或已取消的任务
          if (existingOrder.tasks.length > 0) {
            await tx.task.deleteMany({
              where: {
                orderId: id,
                status: {
                  in: ['COMPLETED', 'CANCELLED'],
                },
              },
            });
          }
        }

        // 删除订单
        await tx.order.delete({
          where: { id },
        });
      });

      const roleText = isAdmin ? '管理员' : isBoss ? '老板' : '用户';
      return {
        message: `订单删除成功`,
        details: isAdmin || isBoss ? `${roleText}强制删除了订单及其所有关联数据` : undefined
      };
    } catch (error: any) {
      // 处理Prisma错误
      if (error.code === 'P2003') {
        throw new AuthorizationError('订单存在关联数据，无法删除。请先处理相关任务或联系管理员');
      }
      if (error.code === 'P2025') {
        throw new NotFoundError('订单不存在或已被删除');
      }
      throw error;
    }
  }

  // 获取订单统计信息
  async getOrderStats(createdById?: string) {
    const where = createdById ? { createdById } : {};
    
    const stats = await prisma.order.groupBy({
      by: ['status'],
      where,
      _count: {
        status: true,
      },
    });

    const totalOrders = await prisma.order.count({ where });
    const totalValue = await prisma.order.aggregate({
      where,
      _sum: {
        price: true,
      },
    });

    const result = {
      totalOrders,
      totalValue: totalValue._sum.price || 0,
      statusBreakdown: stats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status;
        return acc;
      }, {} as Record<string, number>),
    };

    return result;
  }
}
