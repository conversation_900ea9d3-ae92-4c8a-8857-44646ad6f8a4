<template>
  <el-dialog
    v-model="visible"
    :title="`${gameName} - 段位管理`"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="showCreateRankDialog = true">
        <el-icon><Plus /></el-icon>
        添加段位
      </el-button>
      <el-button @click="showBatchCreateDialog = true">
        <el-icon><Upload /></el-icon>
        批量添加
      </el-button>
      <el-button @click="handleRefresh">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 段位列表 -->
    <el-table 
      :data="ranks" 
      :loading="loading"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="level" label="等级" width="80" align="center" sortable />
      
      <el-table-column prop="displayName" label="段位名称" min-width="150">
        <template #default="{ row }">
          <div class="rank-info">
            <img v-if="row.icon" :src="row.icon" class="rank-icon" />
            <div>
              <div class="rank-name">{{ row.displayName }}</div>
              <div class="rank-code">{{ row.name }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column prop="difficultyMultiplier" label="难度系数" width="120" align="center">
        <template #default="{ row }">
          <el-tag :type="getDifficultyType(row.difficultyMultiplier)">
            {{ row.difficultyMultiplier.toFixed(1) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
      
      <el-table-column label="使用情况" width="120" align="center">
        <template #default="{ row }">
          <div class="usage-info">
            <div>起始: {{ row._count?.ordersFrom || 0 }}</div>
            <div>目标: {{ row._count?.ordersTo || 0 }}</div>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="状态" width="80" align="center">
        <template #default="{ row }">
          <el-switch
            v-model="row.isActive"
            @change="handleToggleStatus(row)"
            :loading="loading"
          />
        </template>
      </el-table-column>
      
      <el-table-column label="操作" width="150" align="center" fixed="right">
        <template #default="{ row }">
          <el-button size="small" type="primary" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="handleDelete(row)"
            :disabled="(row._count?.ordersFrom || 0) + (row._count?.ordersTo || 0) > 0"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑段位对话框 -->
    <RankEditDialog
      v-model="showCreateRankDialog"
      :game-id="gameId"
      :rank-id="editingRankId"
      @success="handleRankDialogSuccess"
    />

    <!-- 批量创建段位对话框 -->
    <BatchCreateRankDialog
      v-model="showBatchCreateDialog"
      :game-id="gameId"
      @success="handleBatchCreateSuccess"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Upload, Refresh } from '@element-plus/icons-vue';
import { useGameStore } from '../stores/game';
import RankEditDialog from './RankEditDialog.vue';
import BatchCreateRankDialog from './BatchCreateRankDialog.vue';
import type { GameRank } from '../types/game';

interface Props {
  modelValue: boolean;
  gameId: string;
  gameName: string;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const gameStore = useGameStore();

// 响应式数据
const loading = ref(false);
const ranks = ref<GameRank[]>([]);
const showCreateRankDialog = ref(false);
const showBatchCreateDialog = ref(false);
const editingRankId = ref<string>('');

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 方法
const fetchRanks = async () => {
  if (!props.gameId) return;
  
  try {
    loading.value = true;
    const result = await gameStore.fetchGameRanks({ 
      gameId: props.gameId,
      limit: 100,
      sortBy: 'level',
      sortOrder: 'asc'
    });
    ranks.value = result.items;
  } catch (error) {
    console.error('获取段位列表失败:', error);
    ElMessage.error('获取段位列表失败');
  } finally {
    loading.value = false;
  }
};

const handleRefresh = () => {
  fetchRanks();
};

const getDifficultyType = (multiplier: number) => {
  if (multiplier <= 1.2) return 'success';
  if (multiplier <= 2.0) return 'warning';
  return 'danger';
};

const handleToggleStatus = async (rank: GameRank) => {
  try {
    loading.value = true;
    await gameStore.updateGameRank(rank.id, { isActive: rank.isActive });
    ElMessage.success(`段位已${rank.isActive ? '启用' : '禁用'}`);
  } catch (error) {
    console.error('更新段位状态失败:', error);
    ElMessage.error('更新段位状态失败');
    // 恢复原状态
    rank.isActive = !rank.isActive;
  } finally {
    loading.value = false;
  }
};

const handleEdit = (rank: GameRank) => {
  editingRankId.value = rank.id;
  showCreateRankDialog.value = true;
};

const handleDelete = async (rank: GameRank) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除段位"${rank.displayName}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    loading.value = true;
    await gameStore.deleteGameRank(rank.id);
    ElMessage.success('段位删除成功');
    await fetchRanks();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除段位失败:', error);
      ElMessage.error('删除段位失败');
    }
  } finally {
    loading.value = false;
  }
};

const handleRankDialogSuccess = () => {
  showCreateRankDialog.value = false;
  editingRankId.value = '';
  fetchRanks();
};

const handleBatchCreateSuccess = () => {
  showBatchCreateDialog.value = false;
  fetchRanks();
};

const handleClose = () => {
  visible.value = false;
  ranks.value = [];
  editingRankId.value = '';
};

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal && props.gameId) {
    fetchRanks();
  }
});
</script>

<style lang="scss" scoped>
.action-bar {
  margin-bottom: 16px;
  display: flex;
  gap: 12px;
}

.rank-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.rank-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 4px;
}

.rank-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.rank-code {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.usage-info {
  font-size: 12px;
  color: var(--el-text-color-regular);
  
  div {
    margin: 2px 0;
  }
}
</style>
