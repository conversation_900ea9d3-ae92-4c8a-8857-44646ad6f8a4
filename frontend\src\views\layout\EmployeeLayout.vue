<template>
  <AppLayout
    theme="employee"
    logo-text="员工端"
    :menu-items="menuItems"
    :show-breadcrumb="true"
    :show-notifications="true"
  >
    <router-view />
  </AppLayout>
</template>

<script setup lang="ts">
import AppLayout from '@/components/Navigation/AppLayout.vue'
import { List, User, Money } from '@element-plus/icons-vue'

// 菜单配置
const menuItems = [
  {
    index: '/employee/available-tasks',
    title: '可接单任务',
    icon: List,
    permission: ['EMPLOYEE']
  },
  {
    index: '/employee/my-tasks',
    title: '我的任务',
    icon: User,
    permission: ['EMPLOYEE']
  },
  {
    index: '/employee/earnings',
    title: '收益统计',
    icon: Money,
    permission: ['EMPLOYEE']
  }
]
</script>
