<template>
  <div class="settings-container">
    <div class="settings-header">
      <h1>系统设置</h1>
      <el-button type="primary" @click="goBack">返回仪表板</el-button>
    </div>

    <div class="settings-content">
      <el-card class="system-info-card">
        <template #header>
          <span>系统信息</span>
        </template>

        <div class="info-grid">
          <div class="info-item">
            <label>系统名称：</label>
            <span>王者荣耀代练任务分发管理系统</span>
          </div>
          <div class="info-item">
            <label>系统版本：</label>
            <span>v1.0.0</span>
          </div>
          <div class="info-item">
            <label>运行环境：</label>
            <span>开发环境</span>
          </div>
          <div class="info-item">
            <label>API地址：</label>
            <span>{{ apiBaseUrl }}</span>
          </div>
          <div class="info-item">
            <label>前端地址：</label>
            <span>{{ frontendUrl }}</span>
          </div>
        </div>
      </el-card>

      <el-card class="preferences-card">
        <template #header>
          <span>偏好设置</span>
        </template>

        <div class="preferences-form">
          <div class="form-item">
            <label>主题模式：</label>
            <el-radio-group v-model="theme">
              <el-radio value="light">浅色模式</el-radio>
              <el-radio value="dark">深色模式</el-radio>
              <el-radio value="auto">跟随系统</el-radio>
            </el-radio-group>
          </div>

          <div class="form-item">
            <label>语言设置：</label>
            <el-select v-model="language" style="width: 200px">
              <el-option label="简体中文" value="zh-CN" />
              <el-option label="English" value="en-US" />
            </el-select>
          </div>

          <div class="form-item">
            <label>自动保存：</label>
            <el-switch v-model="autoSave" />
          </div>

          <div class="form-item">
            <label>消息通知：</label>
            <el-switch v-model="notifications" />
          </div>
        </div>

        <div class="form-actions">
          <el-button type="primary" @click="saveSettings">保存设置</el-button>
          <el-button @click="resetSettings">重置设置</el-button>
        </div>
      </el-card>

      <el-card class="danger-zone-card">
        <template #header>
          <span style="color: #f56c6c;">危险操作</span>
        </template>

        <div class="danger-actions">
          <div class="danger-item">
            <div>
              <h4>清除缓存数据</h4>
              <p>清除本地存储的所有缓存数据</p>
            </div>
            <el-button type="warning" @click="clearCache">清除缓存</el-button>
          </div>

          <div class="danger-item">
            <div>
              <h4>重置系统设置</h4>
              <p>将所有设置恢复到默认状态</p>
            </div>
            <el-button type="danger" @click="resetSystem">重置系统</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

// 系统信息
const apiBaseUrl = ref('http://localhost:3000')
const frontendUrl = ref(window.location.origin)

// 设置项
const theme = ref('light')
const language = ref('zh-CN')
const autoSave = ref(true)
const notifications = ref(true)

onMounted(() => {
  loadSettings()
})

const loadSettings = () => {
  const settings = localStorage.getItem('settings')
  if (settings) {
    try {
      const parsed = JSON.parse(settings)
      theme.value = parsed.theme || 'light'
      language.value = parsed.language || 'zh-CN'
      autoSave.value = parsed.autoSave !== false
      notifications.value = parsed.notifications !== false
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }
}

const saveSettings = () => {
  const settings = {
    theme: theme.value,
    language: language.value,
    autoSave: autoSave.value,
    notifications: notifications.value
  }

  localStorage.setItem('settings', JSON.stringify(settings))
  ElMessage.success('设置保存成功')
}

const resetSettings = () => {
  theme.value = 'light'
  language.value = 'zh-CN'
  autoSave.value = true
  notifications.value = true
  ElMessage.info('设置已重置')
}

const clearCache = async () => {
  try {
    await ElMessageBox.confirm('确定要清除所有缓存数据吗？', '确认操作', {
      type: 'warning'
    })

    // 保留登录信息，清除其他缓存
    const token = localStorage.getItem('token')
    const user = localStorage.getItem('user')
    const settings = localStorage.getItem('settings')

    localStorage.clear()

    if (token) localStorage.setItem('token', token)
    if (user) localStorage.setItem('user', user)
    if (settings) localStorage.setItem('settings', settings)

    ElMessage.success('缓存清除成功')
  } catch {
    // 用户取消操作
  }
}

const resetSystem = async () => {
  try {
    await ElMessageBox.confirm('确定要重置系统设置吗？这将清除所有数据并退出登录！', '危险操作', {
      type: 'error'
    })

    localStorage.clear()
    ElMessage.success('系统已重置')
    router.push('/login')
  } catch {
    // 用户取消操作
  }
}

const goBack = () => {
  router.push('/dashboard')
}
</script>

<style lang="scss" scoped>
.settings-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: var(--spacing-lg);
}

.settings-header {
  @include flex(row, space-between, center);
  margin-bottom: var(--spacing-xl);

  h1 {
    color: var(--primary-color);
    font-size: 2rem;
  }
}

.settings-content {
  max-width: 1000px;
  margin: 0 auto;
  display: grid;
  gap: var(--spacing-xl);
}

.info-grid {
  display: grid;
  gap: var(--spacing-md);
}

.info-item {
  @include flex(row, flex-start, center);

  label {
    min-width: 120px;
    font-weight: 500;
    color: var(--text-color-primary);
  }

  span {
    color: var(--text-color-regular);
  }
}

.preferences-form {
  display: grid;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.form-item {
  @include flex(row, flex-start, center);

  label {
    min-width: 120px;
    font-weight: 500;
    color: var(--text-color-primary);
  }
}

.form-actions {
  @include flex(row, flex-start, center);
  gap: var(--spacing-md);
}

.danger-zone-card {
  border-color: #f56c6c;
}

.danger-actions {
  display: grid;
  gap: var(--spacing-lg);
}

.danger-item {
  @include flex(row, space-between, center);
  padding: var(--spacing-md);
  border: 1px solid #f56c6c;
  border-radius: var(--border-radius-medium);
  background: #fef0f0;

  h4 {
    margin: 0 0 var(--spacing-xs) 0;
    color: #f56c6c;
  }

  p {
    margin: 0;
    color: var(--text-color-secondary);
    font-size: 0.9rem;
  }
}

@include respond-below(md) {
  .settings-header {
    @include flex(column, center, center);
    gap: var(--spacing-md);

    h1 {
      font-size: 1.5rem;
    }
  }

  .info-item,
  .form-item {
    @include flex(column, flex-start, flex-start);

    label {
      margin-bottom: var(--spacing-xs);
    }
  }

  .danger-item {
    @include flex(column, flex-start, flex-start);
    gap: var(--spacing-md);
  }
}
</style>
