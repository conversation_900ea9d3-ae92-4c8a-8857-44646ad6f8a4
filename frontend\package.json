{"name": "game-boost-frontend", "version": "1.0.0", "description": "王者荣耀代练任务分发管理系统 - 前端界面", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.6.0", "element-plus": "^2.4.4", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.1.7", "socket.io-client": "^4.7.4", "vue": "^3.3.8", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.4", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.54.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "sass": "^1.69.5", "typescript": "^5.3.2", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.6", "vue-tsc": "^3.0.3"}, "engines": {"node": ">=18.0.0"}}