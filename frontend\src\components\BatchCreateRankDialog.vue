<template>
  <el-dialog
    v-model="visible"
    title="批量添加段位"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="batch-create-content">
      <!-- 游戏模板选择 -->
      <div class="template-section">
        <h4>选择游戏模板</h4>
        <el-radio-group v-model="selectedTemplate" @change="handleTemplateChange">
          <el-radio-button label="wzry">王者荣耀</el-radio-button>
          <el-radio-button label="lol">英雄联盟</el-radio-button>
          <el-radio-button label="ys">原神</el-radio-button>
          <el-radio-button label="custom">自定义</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 段位预览 -->
      <div class="preview-section">
        <h4>段位预览</h4>
        <el-table :data="rankList" stripe style="width: 100%" max-height="300">
          <el-table-column prop="level" label="等级" width="80" align="center" />
          <el-table-column prop="name" label="段位名称" width="120" />
          <el-table-column prop="displayName" label="显示名称" width="120" />
          <el-table-column prop="difficultyMultiplier" label="难度系数" width="100" align="center">
            <template #default="{ row }">
              {{ row.difficultyMultiplier.toFixed(1) }}
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="150" show-overflow-tooltip />
          <el-table-column label="操作" width="80" align="center">
            <template #default="{ row, $index }">
              <el-button size="small" type="danger" @click="removeRank($index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 自定义添加 -->
      <div v-if="selectedTemplate === 'custom'" class="custom-section">
        <h4>添加自定义段位</h4>
        <el-form :model="customRank" :rules="customRules" ref="customFormRef" inline>
          <el-form-item label="等级" prop="level">
            <el-input-number v-model="customRank.level" :min="1" :max="100" />
          </el-form-item>
          <el-form-item label="名称" prop="name">
            <el-input v-model="customRank.name" placeholder="如: bronze_1" />
          </el-form-item>
          <el-form-item label="显示名称" prop="displayName">
            <el-input v-model="customRank.displayName" placeholder="如: 青铜I" />
          </el-form-item>
          <el-form-item label="难度系数" prop="difficultyMultiplier">
            <el-input-number v-model="customRank.difficultyMultiplier" :min="0.1" :max="10" :step="0.1" :precision="1" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="addCustomRank">添加</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading" :disabled="rankList.length === 0">
        批量创建 ({{ rankList.length }}个)
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, computed, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useGameStore } from '../stores/game';
import type { CreateGameRankRequest } from '../types/game';

export default defineComponent({
  name: 'BatchCreateRankDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    gameId: {
      type: String,
      required: true
    }
  },
  emits: ['update:modelValue', 'success'],
  setup(props, { emit }) {

const gameStore = useGameStore();

// 响应式数据
const loading = ref(false);
const selectedTemplate = ref('wzry');
const rankList = ref<CreateGameRankRequest[]>([]);
const customFormRef = ref();

// 自定义段位表单
const customRank = reactive({
  level: 1,
  name: '',
  displayName: '',
  difficultyMultiplier: 1.0,
  description: ''
});

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 表单验证规则
const customRules = {
  level: [
    { required: true, message: '请输入等级', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '等级必须在1-100之间', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入段位名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '段位名称只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  difficultyMultiplier: [
    { required: true, message: '请输入难度系数', trigger: 'blur' },
    { type: 'number', min: 0.1, max: 10, message: '难度系数必须在0.1-10之间', trigger: 'blur' }
  ]
};

// 游戏模板数据
const gameTemplates = {
  wzry: [
    { level: 1, name: 'bronze_3', displayName: '青铜III', difficultyMultiplier: 1.0, description: '青铜段位' },
    { level: 2, name: 'bronze_2', displayName: '青铜II', difficultyMultiplier: 1.1, description: '青铜段位' },
    { level: 3, name: 'bronze_1', displayName: '青铜I', difficultyMultiplier: 1.2, description: '青铜段位' },
    { level: 4, name: 'silver_3', displayName: '白银III', difficultyMultiplier: 1.3, description: '白银段位' },
    { level: 5, name: 'silver_2', displayName: '白银II', difficultyMultiplier: 1.4, description: '白银段位' },
    { level: 6, name: 'silver_1', displayName: '白银I', difficultyMultiplier: 1.5, description: '白银段位' },
    { level: 7, name: 'gold_3', displayName: '黄金III', difficultyMultiplier: 1.6, description: '黄金段位' },
    { level: 8, name: 'gold_2', displayName: '黄金II', difficultyMultiplier: 1.7, description: '黄金段位' },
    { level: 9, name: 'gold_1', displayName: '黄金I', difficultyMultiplier: 1.8, description: '黄金段位' },
    { level: 10, name: 'platinum_3', displayName: '铂金III', difficultyMultiplier: 2.0, description: '铂金段位' },
    { level: 11, name: 'platinum_2', displayName: '铂金II', difficultyMultiplier: 2.2, description: '铂金段位' },
    { level: 12, name: 'platinum_1', displayName: '铂金I', difficultyMultiplier: 2.4, description: '铂金段位' },
    { level: 13, name: 'diamond_3', displayName: '钻石III', difficultyMultiplier: 2.6, description: '钻石段位' },
    { level: 14, name: 'diamond_2', displayName: '钻石II', difficultyMultiplier: 2.8, description: '钻石段位' },
    { level: 15, name: 'diamond_1', displayName: '钻石I', difficultyMultiplier: 3.0, description: '钻石段位' },
    { level: 16, name: 'master', displayName: '大师', difficultyMultiplier: 3.5, description: '大师段位' },
    { level: 17, name: 'grandmaster', displayName: '宗师', difficultyMultiplier: 4.0, description: '宗师段位' },
    { level: 18, name: 'challenger', displayName: '王者', difficultyMultiplier: 5.0, description: '王者段位' }
  ],
  lol: [
    { level: 1, name: 'iron_4', displayName: '坚韧黑铁IV', difficultyMultiplier: 1.0, description: '黑铁段位' },
    { level: 2, name: 'iron_3', displayName: '坚韧黑铁III', difficultyMultiplier: 1.1, description: '黑铁段位' },
    { level: 3, name: 'iron_2', displayName: '坚韧黑铁II', difficultyMultiplier: 1.2, description: '黑铁段位' },
    { level: 4, name: 'iron_1', displayName: '坚韧黑铁I', difficultyMultiplier: 1.3, description: '黑铁段位' },
    { level: 5, name: 'bronze_4', displayName: '英勇黄铜IV', difficultyMultiplier: 1.4, description: '黄铜段位' },
    { level: 6, name: 'bronze_3', displayName: '英勇黄铜III', difficultyMultiplier: 1.5, description: '黄铜段位' },
    { level: 7, name: 'bronze_2', displayName: '英勇黄铜II', difficultyMultiplier: 1.6, description: '黄铜段位' },
    { level: 8, name: 'bronze_1', displayName: '英勇黄铜I', difficultyMultiplier: 1.7, description: '黄铜段位' },
    { level: 9, name: 'silver_4', displayName: '不屈白银IV', difficultyMultiplier: 1.8, description: '白银段位' },
    { level: 10, name: 'silver_3', displayName: '不屈白银III', difficultyMultiplier: 1.9, description: '白银段位' },
    { level: 11, name: 'silver_2', displayName: '不屈白银II', difficultyMultiplier: 2.0, description: '白银段位' },
    { level: 12, name: 'silver_1', displayName: '不屈白银I', difficultyMultiplier: 2.1, description: '白银段位' },
    { level: 13, name: 'gold_4', displayName: '荣耀黄金IV', difficultyMultiplier: 2.2, description: '黄金段位' },
    { level: 14, name: 'gold_3', displayName: '荣耀黄金III', difficultyMultiplier: 2.3, description: '黄金段位' },
    { level: 15, name: 'gold_2', displayName: '荣耀黄金II', difficultyMultiplier: 2.4, description: '黄金段位' },
    { level: 16, name: 'gold_1', displayName: '荣耀黄金I', difficultyMultiplier: 2.5, description: '黄金段位' },
    { level: 17, name: 'platinum_4', displayName: '华贵铂金IV', difficultyMultiplier: 2.6, description: '铂金段位' },
    { level: 18, name: 'platinum_3', displayName: '华贵铂金III', difficultyMultiplier: 2.7, description: '铂金段位' },
    { level: 19, name: 'platinum_2', displayName: '华贵铂金II', difficultyMultiplier: 2.8, description: '铂金段位' },
    { level: 20, name: 'platinum_1', displayName: '华贵铂金I', difficultyMultiplier: 2.9, description: '铂金段位' },
    { level: 21, name: 'diamond_4', displayName: '璀璨钻石IV', difficultyMultiplier: 3.0, description: '钻石段位' },
    { level: 22, name: 'diamond_3', displayName: '璀璨钻石III', difficultyMultiplier: 3.2, description: '钻石段位' },
    { level: 23, name: 'diamond_2', displayName: '璀璨钻石II', difficultyMultiplier: 3.4, description: '钻石段位' },
    { level: 24, name: 'diamond_1', displayName: '璀璨钻石I', difficultyMultiplier: 3.6, description: '钻石段位' },
    { level: 25, name: 'master', displayName: '超凡大师', difficultyMultiplier: 4.0, description: '大师段位' },
    { level: 26, name: 'grandmaster', displayName: '傲世宗师', difficultyMultiplier: 4.5, description: '宗师段位' },
    { level: 27, name: 'challenger', displayName: '最强王者', difficultyMultiplier: 5.0, description: '王者段位' }
  ],
  ys: [
    { level: 1, name: 'ar_10', displayName: 'AR10', difficultyMultiplier: 1.0, description: '冒险等阶10' },
    { level: 2, name: 'ar_15', displayName: 'AR15', difficultyMultiplier: 1.1, description: '冒险等阶15' },
    { level: 3, name: 'ar_20', displayName: 'AR20', difficultyMultiplier: 1.2, description: '冒险等阶20' },
    { level: 4, name: 'ar_25', displayName: 'AR25', difficultyMultiplier: 1.3, description: '冒险等阶25' },
    { level: 5, name: 'ar_30', displayName: 'AR30', difficultyMultiplier: 1.4, description: '冒险等阶30' },
    { level: 6, name: 'ar_35', displayName: 'AR35', difficultyMultiplier: 1.5, description: '冒险等阶35' },
    { level: 7, name: 'ar_40', displayName: 'AR40', difficultyMultiplier: 1.6, description: '冒险等阶40' },
    { level: 8, name: 'ar_45', displayName: 'AR45', difficultyMultiplier: 1.8, description: '冒险等阶45' },
    { level: 9, name: 'ar_50', displayName: 'AR50', difficultyMultiplier: 2.0, description: '冒险等阶50' },
    { level: 10, name: 'ar_55', displayName: 'AR55', difficultyMultiplier: 2.5, description: '冒险等阶55' },
    { level: 11, name: 'ar_60', displayName: 'AR60', difficultyMultiplier: 3.0, description: '冒险等阶60' }
  ]
};

// 方法
const handleTemplateChange = () => {
  if (selectedTemplate.value !== 'custom') {
    const template = gameTemplates[selectedTemplate.value as keyof typeof gameTemplates];
    rankList.value = template.map(rank => ({
      gameId: props.gameId,
      ...rank
    }));
  } else {
    rankList.value = [];
  }
};

const addCustomRank = async () => {
  try {
    await customFormRef.value.validate();
    
    // 检查等级是否重复
    if (rankList.value.some(rank => rank.level === customRank.level)) {
      ElMessage.error('该等级已存在');
      return;
    }
    
    // 检查名称是否重复
    if (rankList.value.some(rank => rank.name === customRank.name)) {
      ElMessage.error('该段位名称已存在');
      return;
    }
    
    rankList.value.push({
      gameId: props.gameId,
      level: customRank.level,
      name: customRank.name,
      displayName: customRank.displayName,
      difficultyMultiplier: customRank.difficultyMultiplier,
      description: customRank.description,
      isActive: true
    });
    
    // 按等级排序
    rankList.value.sort((a, b) => a.level - b.level);
    
    // 重置表单
    Object.assign(customRank, {
      level: Math.max(...rankList.value.map(r => r.level)) + 1,
      name: '',
      displayName: '',
      difficultyMultiplier: 1.0,
      description: ''
    });
    
    customFormRef.value.clearValidate();
    ElMessage.success('段位添加成功');
  } catch (error) {
    // 验证失败
  }
};

const removeRank = (index: number) => {
  rankList.value.splice(index, 1);
};

const handleSubmit = async () => {
  if (rankList.value.length === 0) {
    ElMessage.error('请至少添加一个段位');
    return;
  }
  
  try {
    loading.value = true;
    
    // 批量创建段位
    for (const rankData of rankList.value) {
      await gameStore.createGameRank(rankData);
    }
    
    ElMessage.success(`成功创建 ${rankList.value.length} 个段位`);
    emit('success');
  } catch (error) {
    console.error('批量创建段位失败:', error);
    ElMessage.error('批量创建段位失败');
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  visible.value = false;
  selectedTemplate.value = 'wzry';
  rankList.value = [];
  Object.assign(customRank, {
    level: 1,
    name: '',
    displayName: '',
    difficultyMultiplier: 1.0,
    description: ''
  });
};

// 初始化
handleTemplateChange();

return {
  loading,
  selectedTemplate,
  rankList,
  customFormRef,
  customRank,
  visible,
  customRules,
  gameTemplates,
  handleTemplateChange,
  addCustomRank,
  removeRank,
  handleSubmit,
  handleClose
};
  }
});
</script>

<style lang="scss" scoped>
.batch-create-content {
  .template-section,
  .preview-section,
  .custom-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }
  
  .custom-section {
    border-top: 1px solid var(--el-border-color-light);
    padding-top: 20px;
  }
}
</style>
