<template>
  <div class="tasks-container">
    <div class="page-header">
      <h1>任务管理</h1>
      <el-button
        type="primary"
        @click="showCreateDialog = true"
        v-permission="['BOSS', 'ADMIN']"
      >
        <el-icon><Plus /></el-icon>
        创建任务
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="任务号、订单号、客户姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="全部状态"
            clearable
            style="width: 120px"
          >
            <el-option label="待处理" value="PENDING" />
            <el-option label="已接受" value="ACCEPTED" />
            <el-option label="进行中" value="IN_PROGRESS" />
            <el-option label="已提交" value="SUBMITTED" />
            <el-option label="已审核" value="APPROVED" />
            <el-option label="已拒绝" value="REJECTED" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item label="分配方式">
          <el-select
            v-model="searchForm.assignType"
            placeholder="全部方式"
            clearable
            style="width: 120px"
          >
            <el-option label="直接分配" value="DIRECT" />
            <el-option label="系统挂单" value="SYSTEM" />
          </el-select>
        </el-form-item>
        <el-form-item label="员工">
          <el-select v-model="searchForm.assigneeId" placeholder="全部员工" clearable filterable>
            <el-option
              v-for="employee in employees"
              :key="employee.id"
              :label="employee.nickname || employee.username"
              :value="employee.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="table-card">
      <el-table
        :data="tasks"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="taskNo" label="任务号" width="120" />
        <el-table-column label="订单信息" width="200">
          <template #default="{ row }">
            <div>
              <div>{{ row.order?.orderNo }}</div>
              <div class="text-gray">{{ row.order?.customerName }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="游戏类型" width="120">
          <template #default="{ row }">
            <el-tag type="primary" size="small" v-if="row.order?.gameType">
              {{ getGameTypeName(row.order.gameType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="分配方式" width="100">
          <template #default="{ row }">
            <el-tag :type="row.assignType === 'DIRECT' ? 'success' : 'info'" size="small">
              {{ row.assignType === 'DIRECT' ? '直接分配' : '系统挂单' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="分配员工" width="120">
          <template #default="{ row }">
            <span v-if="row.assignee">
              {{ row.assignee.nickname || row.assignee.username }}
            </span>
            <span v-else class="text-gray">未分配</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="佣金" width="100">
          <template #default="{ row }">
            <span v-if="row.commission">¥{{ row.commission }}</span>
            <span v-else class="text-gray">未设置</span>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button
              v-if="row.status === 'SUBMITTED'"
              size="small"
              type="success"
              @click="handleReview(row)"
              v-permission="['BOSS', 'ADMIN']"
            >
              审核
            </el-button>
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(row)"
              v-permission="['BOSS', 'ADMIN']"
              :disabled="row.status === 'COMPLETED' || row.status === 'CANCELLED'"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleCancel(row)"
              v-permission="['BOSS', 'ADMIN']"
              :disabled="row.status === 'COMPLETED' || row.status === 'CANCELLED'"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建任务对话框 -->
    <CreateTaskDialog
      v-model="showCreateDialog"
      @success="handleCreateSuccess"
    />

    <!-- 任务详情对话框 -->
    <TaskDetailDialog
      v-model="showDetailDialog"
      :task-id="selectedTaskId"
    />

    <!-- 编辑任务对话框 -->
    <TaskEditDialog
      v-model="showEditDialog"
      :task-id="selectedTaskId"
      @success="handleEditSuccess"
    />

    <!-- 任务审核对话框 -->
    <el-dialog v-model="showReviewDialog" title="任务审核" width="500px">
      <div v-if="reviewTask" class="review-content">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="任务号">{{ reviewTask.taskNo }}</el-descriptions-item>
          <el-descriptions-item label="员工">
            {{ reviewTask.assignee?.nickname || reviewTask.assignee?.username }}
          </el-descriptions-item>
          <el-descriptions-item label="佣金">¥{{ reviewTask.commission }}</el-descriptions-item>
        </el-descriptions>

        <el-form :model="reviewForm" label-width="80px" style="margin-top: 20px;">
          <el-form-item label="审核结果" required>
            <el-radio-group v-model="reviewForm.approved">
              <el-radio :value="true">通过</el-radio>
              <el-radio :value="false">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见">
            <el-input
              v-model="reviewForm.feedback"
              type="textarea"
              :rows="3"
              placeholder="请输入审核意见（可选）"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="showReviewDialog = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmReview" :loading="reviewing">
          确认审核
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, toRaw } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { taskApi, type TaskQuery } from '@/api/tasks'
import { userApi } from '@/api/users'
import type { Task, TaskStatus, AssignType, User } from '@/types'
import { formatDate } from '@/utils/date'
import CreateTaskDialog from '@/components/CreateTaskDialog.vue'
import TaskDetailDialog from '@/components/TaskDetailDialog.vue'
import TaskEditDialog from '@/components/TaskEditDialog.vue'

// 游戏类型名称映射
const getGameTypeName = (gameType: string) => {
  const gameTypeMap: Record<string, string> = {
    'wzry': '王者荣耀',
    'lol': '英雄联盟',
    'ys': '原神',
    'mc': '鸣潮',
    'valorant': 'Valorant',
    'csgo': 'CS:GO',
    'dota2': 'Dota2',
    'overwatch': '守望先锋',
    'apex': 'Apex英雄',
    'pubg': '绝地求生',
    'genshin': '原神',
    'honkai': '崩坏：星穹铁道'
  }
  return gameTypeMap[gameType] || gameType
}

// 响应式数据
const loading = ref(false)
const tasks = ref<Task[]>([])
const employees = ref<User[]>([])
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showEditDialog = ref(false)
const showReviewDialog = ref(false)
const selectedTaskId = ref<string | null>(null)
const reviewTask = ref<Task | null>(null)
const reviewing = ref(false)

const reviewForm = reactive({
  approved: true,
  feedback: ''
})

const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

const searchForm = reactive<TaskQuery>({
  keyword: '',
  status: undefined,
  assignType: undefined,
  assigneeId: undefined
})

// 获取任务列表
const fetchTasks = async () => {
  try {
    loading.value = true
    const response = await taskApi.getTasks({
      ...searchForm,
      page: pagination.page,
      limit: pagination.limit
    })

    if (response.success && response.data) {
      tasks.value = response.data.items
      pagination.total = response.data.pagination.total
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 获取员工列表
const fetchEmployees = async () => {
  try {
    const response = await userApi.getUsers({
      role: 'EMPLOYEE',
      status: 'ACTIVE',
      limit: 100
    })

    if (response.success && response.data) {
      employees.value = response.data.items
    }
  } catch (error) {
    console.error('获取员工列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchTasks()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    assignType: undefined,
    assigneeId: undefined
  })
  pagination.page = 1
  fetchTasks()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchTasks()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchTasks()
}

// 查看任务
const handleView = (task: Task) => {
  selectedTaskId.value = task.id
  showDetailDialog.value = true
}

// 编辑任务
const handleEdit = (task: Task) => {
  selectedTaskId.value = task.id
  showEditDialog.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  fetchTasks()
}

// 取消任务
const handleCancel = async (task: Task) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消任务 ${task.taskNo} 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await taskApi.cancelTask(task.id)
    if (response.success) {
      ElMessage.success('任务取消成功')
      fetchTasks()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任务失败:', error)
      ElMessage.error('取消任务失败')
    }
  }
}

// 审核任务
const handleReview = (task: Task) => {
  reviewTask.value = task
  reviewForm.approved = true
  reviewForm.feedback = ''
  showReviewDialog.value = true
}

// 确认审核
const handleConfirmReview = async () => {
  if (!reviewTask.value) return

  try {
    reviewing.value = true
    const response = await taskApi.reviewTask(reviewTask.value.id, {
      approved: reviewForm.approved,
      feedback: reviewForm.feedback
    })

    if (response.success) {
      ElMessage.success(`任务${reviewForm.approved ? '审核通过' : '审核拒绝'}`)
      showReviewDialog.value = false
      fetchTasks()
    }
  } catch (error) {
    console.error('任务审核失败:', error)
    ElMessage.error('任务审核失败')
  } finally {
    reviewing.value = false
  }
}

// 创建任务成功回调
const handleCreateSuccess = () => {
  showCreateDialog.value = false
  fetchTasks()
}

// 状态相关方法
const getStatusType = (status: TaskStatus) => {
  const statusMap = {
    PENDING: 'warning',
    ACCEPTED: 'info',
    IN_PROGRESS: 'primary',
    SUBMITTED: 'warning',
    APPROVED: 'success',
    REJECTED: 'danger',
    COMPLETED: 'success',
    CANCELLED: 'info'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: TaskStatus) => {
  const statusMap = {
    PENDING: '待处理',
    ACCEPTED: '已接受',
    IN_PROGRESS: '进行中',
    SUBMITTED: '已提交',
    APPROVED: '已审核',
    REJECTED: '已拒绝',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

// 初始化
onMounted(() => {
  fetchTasks()
  fetchEmployees()
})
</script>

<style lang="scss" scoped>
.tasks-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    color: var(--text-primary);
  }
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}

.text-gray {
  color: #909399;
  font-size: 12px;
}
</style>
