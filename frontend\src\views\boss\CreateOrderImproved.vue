<template>
  <div class="create-order-container">
    <div class="page-header">
      <h1>创建订单</h1>
      <el-button @click="$router.back()">返回</el-button>
    </div>

    <el-card class="create-order-card">
      <!-- 游戏选择 -->
      <div v-if="!selectedGame" class="game-selection">
        <h3 class="section-title">选择游戏</h3>
        <div v-if="loading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          正在加载游戏列表...
        </div>
        <div v-else-if="activeGames.length > 0">
          <el-row :gutter="16">
            <el-col v-for="game in activeGames" :key="game.id" :span="8">
              <el-card 
                class="game-card" 
                :body-style="{ padding: '20px' }"
                @click="selectGame(game)"
                shadow="hover"
              >
                <div class="game-content">
                  <el-avatar
                    :src="game.icon"
                    :alt="game.displayName"
                    shape="square"
                    size="large"
                  >
                    {{ game.displayName.charAt(0) }}
                  </el-avatar>
                  <h4>{{ game.displayName }}</h4>
                  <p>{{ game.description }}</p>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        <div v-else class="no-games">
          <el-empty description="暂无可用游戏" />
        </div>
      </div>

      <!-- 订单创建表单 -->
      <div v-else class="order-form-container">
        <div class="form-header">
          <div class="game-info">
            <el-avatar
              :src="selectedGame.icon"
              :alt="selectedGame.displayName"
              shape="square"
              size="default"
            >
              {{ selectedGame.displayName.charAt(0) }}
            </el-avatar>
            <div class="game-details">
              <h3>{{ selectedGame.displayName }}</h3>
              <p>{{ selectedGame.description }}</p>
            </div>
          </div>
          <el-button @click="changeGame" type="text">
            重新选择游戏
          </el-button>
        </div>

        <!-- 改进的双栏布局 -->
        <div v-if="gameFormFields.length > 0" class="improved-layout">
          <div class="layout-container">
            <!-- 左侧：表单填写区域 -->
            <div class="form-section">
              <div class="section-header">
                <h4><el-icon><Edit /></el-icon> 填写订单信息</h4>
                <el-button type="primary" @click="handleSubmit" :loading="submitting">
                  创建订单
                </el-button>
              </div>
              
              <el-form
                ref="formRef"
                :model="formData"
                :rules="dynamicFormRules"
                label-width="90px"
                class="compact-form"
              >
                <el-row :gutter="16">
                  <el-col
                    v-for="field in visibleFormFields"
                    :key="field.id"
                    :span="getFieldSpan(field.fieldType)"
                  >
                    <el-form-item
                      :label="field.fieldLabel"
                      :prop="field.fieldKey"
                      :required="field.isRequired"
                      class="compact-form-item"
                    >
                      <!-- 各种字段类型的输入控件 -->
                      <template v-if="field.fieldType === 'IMAGE'">
                        <el-upload
                          class="image-uploader"
                          action="/api/v1/upload"
                          :show-file-list="false"
                          :on-success="(response) => handleImageUpload(response, field.fieldKey)"
                          :before-upload="beforeImageUpload"
                        >
                          <img
                            v-if="formData[field.fieldKey]"
                            :src="formData[field.fieldKey]"
                            class="uploaded-image"
                          />
                          <div v-else class="upload-placeholder">
                            <el-icon class="upload-icon"><Plus /></el-icon>
                            <div class="upload-text">点击上传图片</div>
                          </div>
                        </el-upload>
                      </template>

                      <template v-else-if="field.fieldType === 'SELECT'">
                        <el-select
                          v-model="formData[field.fieldKey]"
                          :placeholder="field.placeholder || `请选择${field.fieldLabel}`"
                          clearable
                          style="width: 100%"
                        >
                          <el-option
                            v-for="option in field.options"
                            :key="option"
                            :label="option"
                            :value="option"
                          />
                        </el-select>
                      </template>

                      <template v-else-if="field.fieldType === 'CHECKBOX'">
                        <el-checkbox-group v-model="formData[field.fieldKey]">
                          <el-checkbox
                            v-for="option in field.options"
                            :key="option"
                            :label="option"
                          >
                            {{ option }}
                          </el-checkbox>
                        </el-checkbox-group>
                      </template>

                      <component
                        v-else
                        :is="getFieldComponent(field.fieldType)"
                        v-model="formData[field.fieldKey]"
                        v-bind="getFieldProps(field)"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>

            <!-- 右侧：字段管理区域 -->
            <div class="management-section">
              <div class="section-header">
                <h4><el-icon><Setting /></el-icon> 字段管理</h4>
                <el-button 
                  type="primary" 
                  :icon="Plus" 
                  @click="showFieldConfigDialog = true"
                  size="small"
                >
                  添加字段
                </el-button>
              </div>

              <!-- 可拖拽的字段列表 -->
              <div class="field-list">
                <draggable
                  v-model="gameFormFields"
                  @end="onFieldOrderChange"
                  item-key="id"
                  class="draggable-container"
                  ghost-class="ghost-item"
                  chosen-class="chosen-item"
                >
                  <template #item="{ element: field }">
                    <div class="field-item">
                      <div class="field-drag-handle">
                        <el-icon><Sort /></el-icon>
                      </div>
                      <div class="field-info">
                        <div class="field-title">
                          <span class="field-name">{{ field.fieldLabel }}</span>
                          <el-tag 
                            :type="field.isRequired ? 'danger' : 'info'" 
                            size="small"
                          >
                            {{ field.isRequired ? '必填' : '可选' }}
                          </el-tag>
                        </div>
                        <div class="field-meta">
                          <el-tag size="small" effect="plain">
                            {{ getFieldTypeLabel(field.fieldType) }}
                          </el-tag>
                          <span class="field-key">{{ field.fieldKey }}</span>
                        </div>
                      </div>
                      <div class="field-actions">
                        <el-button 
                          type="text" 
                          size="small"
                          @click="editField(field)"
                          :icon="Edit"
                        />
                        <el-button 
                          type="text" 
                          size="small"
                          @click="deleteField(field)"
                          :icon="Delete"
                          style="color: #f56c6c"
                        />
                      </div>
                    </div>
                  </template>
                </draggable>
              </div>
            </div>
          </div>
        </div>

        <!-- 无字段提示 -->
        <div v-else class="no-fields-tip">
          <el-alert
            title="该游戏暂未配置表单字段"
            type="warning"
            :closable="false"
            show-icon
          >
            <template #default>
              您可以配置字段后创建订单
            </template>
          </el-alert>
          <div style="margin-top: 20px;">
            <el-button type="primary" @click="showFieldConfigDialog = true">
              配置字段
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 字段配置对话框 -->
    <el-dialog
      v-model="showFieldConfigDialog"
      :title="editingField ? '编辑字段' : '添加字段'"
      width="600px"
      :close-on-click-modal="false"
      @close="resetFieldForm"
    >
      <el-form
        ref="fieldFormRef"
        :model="fieldForm"
        :rules="fieldFormRules"
        label-width="100px"
      >
        <el-form-item label="字段标签" prop="fieldLabel">
          <el-input
            v-model="fieldForm.fieldLabel"
            placeholder="请输入字段显示名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="字段键名" prop="fieldKey">
          <el-input
            v-model="fieldForm.fieldKey"
            placeholder="请输入字段键名（英文）"
            clearable
            :disabled="!!editingField"
          />
        </el-form-item>

        <el-form-item label="字段类型" prop="fieldType">
          <el-select
            v-model="fieldForm.fieldType"
            placeholder="请选择字段类型"
            style="width: 100%"
            @change="onFieldTypeChange"
          >
            <el-option label="单行文本" value="TEXT" />
            <el-option label="多行文本" value="TEXTAREA" />
            <el-option label="数字" value="NUMBER" />
            <el-option label="密码" value="PASSWORD" />
            <el-option label="下拉选择" value="SELECT" />
            <el-option label="多选框" value="CHECKBOX" />
            <el-option label="图片上传" value="IMAGE" />
          </el-select>
        </el-form-item>

        <el-form-item label="是否必填" prop="isRequired">
          <el-switch v-model="fieldForm.isRequired" />
        </el-form-item>

        <el-form-item label="占位符" prop="placeholder">
          <el-input
            v-model="fieldForm.placeholder"
            placeholder="请输入占位符文本"
            clearable
          />
        </el-form-item>

        <!-- 选择类型字段的选项配置 -->
        <el-form-item
          v-if="fieldForm.fieldType === 'SELECT' || fieldForm.fieldType === 'CHECKBOX'"
          label="选项配置"
          prop="options"
        >
          <div class="options-config">
            <div
              v-for="(option, index) in fieldForm.options"
              :key="index"
              class="option-item"
            >
              <el-input
                v-model="fieldForm.options[index]"
                placeholder="请输入选项"
                style="flex: 1"
              />
              <el-button
                type="danger"
                :icon="Delete"
                @click="removeOption(index)"
                style="margin-left: 8px"
              />
            </div>
            <el-button
              type="primary"
              :icon="Plus"
              @click="addOption"
              style="width: 100%; margin-top: 8px"
            >
              添加选项
            </el-button>
          </div>
        </el-form-item>

        <!-- 数字类型的配置 -->
        <template v-if="fieldForm.fieldType === 'NUMBER'">
          <el-form-item label="最小值">
            <el-input-number
              v-model="fieldForm.config.min"
              placeholder="最小值"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="最大值">
            <el-input-number
              v-model="fieldForm.config.max"
              placeholder="最大值"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="步长">
            <el-input-number
              v-model="fieldForm.config.step"
              :min="0.01"
              :step="0.01"
              placeholder="步长"
              style="width: 100%"
            />
          </el-form-item>
        </template>

        <!-- 文本域的配置 -->
        <el-form-item v-if="fieldForm.fieldType === 'TEXTAREA'" label="行数">
          <el-input-number
            v-model="fieldForm.config.rows"
            :min="2"
            :max="10"
            placeholder="行数"
            style="width: 100%"
          />
        </el-form-item>

        <!-- 文本类型的最大长度 -->
        <el-form-item
          v-if="fieldForm.fieldType === 'TEXT' || fieldForm.fieldType === 'TEXTAREA'"
          label="最大长度"
        >
          <el-input-number
            v-model="fieldForm.config.maxLength"
            :min="1"
            placeholder="最大字符数"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showFieldConfigDialog = false">取消</el-button>
          <el-button type="primary" @click="saveField" :loading="saving">
            {{ editingField ? '更新' : '添加' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Loading, Delete, Sort, Edit, Setting } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { getActiveGames } from '../../api/game'
import { getActiveFieldsByGameId, createGameFormField, updateGameFormField, deleteGameFormField } from '../../api/gameFormFields'
import { orderApi } from '../../api/orders'

// 基础数据
const router = useRouter()
const loading = ref(false)
const submitting = ref(false)
const saving = ref(false)
const activeGames = ref([])
const selectedGame = ref(null)
const gameFormFields = ref([])
const formData = ref({})
const formRef = ref()
const fieldFormRef = ref()
const showFieldConfigDialog = ref(false)
const editingField = ref(null)

// 字段表单数据
const fieldForm = ref({
  fieldLabel: '',
  fieldKey: '',
  fieldType: 'TEXT',
  isRequired: false,
  placeholder: '',
  options: [],
  config: {
    min: null,
    max: null,
    step: 1,
    rows: 3,
    maxLength: null
  }
})

// 字段表单验证规则
const fieldFormRules = {
  fieldLabel: [
    { required: true, message: '请输入字段标签', trigger: 'blur' }
  ],
  fieldKey: [
    { required: true, message: '请输入字段键名', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '字段键名必须以字母开头，只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  fieldType: [
    { required: true, message: '请选择字段类型', trigger: 'change' }
  ]
}

// 过滤可见的表单字段（隐藏游戏类型字段）
const visibleFormFields = computed(() => {
  return gameFormFields.value.filter(field => {
    // 隐藏游戏类型相关字段，因为已经通过游戏选择确定了
    const gameTypeKeys = ['gameType', 'game_type', '游戏类型', 'gameId', 'game_id']
    return !gameTypeKeys.includes(field.fieldKey) &&
           !gameTypeKeys.includes(field.fieldLabel)
  })
})

// 计算字段跨度
const getFieldSpan = (fieldType: string) => {
  switch (fieldType) {
    case 'TEXTAREA':
      return 24 // 全宽
    case 'IMAGE':
      return 8  // 图片上传较小
    case 'TEXT':
    case 'PASSWORD':
    case 'NUMBER':
    case 'SELECT':
      return 12 // 半宽
    case 'CHECKBOX':
      return 24 // 多选框全宽
    default:
      return 12
  }
}

// 获取字段组件
const getFieldComponent = (fieldType: string) => {
  const componentMap = {
    'TEXT': 'el-input',
    'TEXTAREA': 'el-input',
    'NUMBER': 'el-input-number',
    'PASSWORD': 'el-input',
    'SELECT': 'el-select',
    'CHECKBOX': 'el-checkbox-group'
  }
  return componentMap[fieldType] || 'el-input'
}

// 获取字段属性
const getFieldProps = (field: any) => {
  const baseProps = {
    placeholder: field.placeholder || `请输入${field.fieldLabel}`,
    clearable: true
  }
  
  if (field.fieldType === 'TEXTAREA') {
    return { ...baseProps, type: 'textarea', rows: 3 }
  }
  if (field.fieldType === 'PASSWORD') {
    return { ...baseProps, type: 'password', showPassword: true }
  }
  if (field.fieldType === 'NUMBER') {
    return { 
      ...baseProps, 
      min: field.config?.min || 0,
      max: field.config?.max || 999999,
      style: 'width: 100%'
    }
  }
  
  return baseProps
}

// 获取字段类型标签
const getFieldTypeLabel = (type: string) => {
  const typeMap = {
    'TEXT': '文本',
    'TEXTAREA': '多行文本',
    'NUMBER': '数字',
    'PASSWORD': '密码',
    'SELECT': '下拉选择',
    'CHECKBOX': '多选'
  }
  return typeMap[type] || type
}

// 拖拽排序处理
const onFieldOrderChange = () => {
  console.log('字段顺序已更改')
  // TODO: 调用API更新字段顺序
}

// 加载游戏列表
const loadActiveGames = async () => {
  try {
    loading.value = true
    const response = await getActiveGames()
    activeGames.value = response || []
  } catch (error) {
    console.error('加载游戏列表失败:', error)
    ElMessage.error('加载游戏列表失败')
  } finally {
    loading.value = false
  }
}

// 加载游戏字段
const loadGameFormFields = async (gameId: number) => {
  try {
    loading.value = true
    const response = await getActiveFieldsByGameId(gameId.toString())
    gameFormFields.value = response || []

    // 初始化表单数据
    const initialData: any = {}
    gameFormFields.value.forEach((field: any) => {
      if (field.fieldType === 'CHECKBOX') {
        initialData[field.fieldKey] = []
      } else {
        initialData[field.fieldKey] = ''
      }
    })
    formData.value = initialData
  } catch (error) {
    console.error('加载字段失败:', error)
    ElMessage.error('加载字段失败')
  } finally {
    loading.value = false
  }
}

// 基础方法
const selectGame = async (game: any) => {
  selectedGame.value = game
  await loadGameFormFields(game.id)

  // 自动设置游戏相关信息到表单数据中
  formData.value['gameId'] = game.id
  formData.value['gameType'] = game.displayName
  formData.value['game_type'] = game.displayName
  formData.value['游戏类型'] = game.displayName
}

const changeGame = () => {
  selectedGame.value = null
  gameFormFields.value = []
  formData.value = {}
}

const editField = (field: any) => {
  editingField.value = field
  fieldForm.value = {
    fieldLabel: field.fieldLabel,
    fieldKey: field.fieldKey,
    fieldType: field.fieldType,
    isRequired: field.isRequired,
    placeholder: field.placeholder || '',
    options: field.options ? [...field.options] : [],
    config: {
      min: field.config?.min || null,
      max: field.config?.max || null,
      step: field.config?.step || 1,
      rows: field.config?.rows || 3,
      maxLength: field.config?.maxLength || null
    }
  }
  showFieldConfigDialog.value = true
}

const deleteField = async (field: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字段 "${field.fieldLabel}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await deleteGameFormField(field.id)
    ElMessage.success('字段删除成功')

    // 重新加载字段列表
    if (selectedGame.value) {
      await loadGameFormFields(selectedGame.value.id)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除字段失败:', error)
      ElMessage.error('删除字段失败')
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()

    submitting.value = true

    // 创建一个辅助函数来查找字段值
    const getFieldValue = (possibleKeys: string[]) => {
      for (const key of possibleKeys) {
        if (formData.value[key] && formData.value[key].toString().trim()) {
          return formData.value[key].toString().trim()
        }
      }
      return ''
    }

    // 将动态表单数据转换为后端期望的格式
    const orderData = {
      // 必需字段 - 使用数据库中实际的字段键名
      customerName: getFieldValue(['customer_name', 'customerName', '客户姓名', '客户名称', '姓名']),
      gameAccount: getFieldValue(['game_account', 'gameAccount', '游戏账号', '账号', '游戏账户']),
      gamePassword: getFieldValue(['game_password', 'gamePassword', '游戏密码', '密码']),
      gameType: selectedGame.value.displayName, // 后端期望gameType而不是gameId
      price: parseFloat(getFieldValue(['price', '价格', '订单价格', '金额']) || '0'),
      priority: 'NORMAL' as any,

      // 可选字段
      customerContact: getFieldValue(['customer_contact', 'customerContact', '联系方式', '联系电话', '手机号']),
      deadline: formData.value['交付时间'] || formData.value['deadline'] || undefined,
      requirements: getFieldValue(['requirements', '特殊要求', '订单备注', '备注']),

      // 兼容字段（后端支持的段位字段）
      currentRank: getFieldValue(['current_rank', 'currentRank', '当前段位', '现有段位']),
      targetRank: getFieldValue(['target_rank', 'targetRank', '目标段位', '目标等级']),

      // 动态表单数据（存储所有表单数据）
      details: formData.value,
      formData: formData.value
    }

    console.log('准备提交的订单数据:', orderData)
    console.log('当前表单数据:', formData.value)
    console.log('选中的游戏:', selectedGame.value)

    // 验证必需字段
    if (!orderData.customerName) {
      ElMessage.error('请填写客户姓名')
      return
    }
    if (!orderData.gameAccount) {
      ElMessage.error('请填写游戏账号')
      return
    }
    if (!orderData.gamePassword) {
      ElMessage.error('请填写游戏密码')
      return
    }
    if (!orderData.price || orderData.price <= 0) {
      ElMessage.error('请填写有效的价格')
      return
    }

    await orderApi.createOrder(orderData as any)
    ElMessage.success('订单创建成功')
    router.push('/boss/orders')
  } catch (error) {
    console.error('创建订单失败:', error)
    ElMessage.error('创建订单失败')
  } finally {
    submitting.value = false
  }
}

// 表单验证规则
const dynamicFormRules = computed(() => {
  const rules: any = {}
  gameFormFields.value.forEach((field: any) => {
    if (field.isRequired) {
      rules[field.fieldKey] = [
        { required: true, message: `请输入${field.fieldLabel}`, trigger: 'blur' }
      ]
    }
  })
  return rules
})

// 字段表单操作方法
const resetFieldForm = () => {
  editingField.value = null
  fieldForm.value = {
    fieldLabel: '',
    fieldKey: '',
    fieldType: 'TEXT',
    isRequired: false,
    placeholder: '',
    options: [],
    config: {
      min: null,
      max: null,
      step: 1,
      rows: 3,
      maxLength: null
    }
  }
  fieldFormRef.value?.clearValidate()
}

const onFieldTypeChange = () => {
  // 当字段类型改变时，重置相关配置
  if (fieldForm.value.fieldType === 'SELECT' || fieldForm.value.fieldType === 'CHECKBOX') {
    if (fieldForm.value.options.length === 0) {
      fieldForm.value.options = ['选项1']
    }
  } else {
    fieldForm.value.options = []
  }
}

const addOption = () => {
  fieldForm.value.options.push(`选项${fieldForm.value.options.length + 1}`)
}

const removeOption = (index: number) => {
  fieldForm.value.options.splice(index, 1)
}

const saveField = async () => {
  try {
    await fieldFormRef.value?.validate()

    saving.value = true

    const fieldData = {
      gameId: selectedGame.value.id,
      fieldLabel: fieldForm.value.fieldLabel,
      fieldKey: fieldForm.value.fieldKey,
      fieldType: fieldForm.value.fieldType as any,
      isRequired: fieldForm.value.isRequired,
      placeholder: fieldForm.value.placeholder,
      options: (fieldForm.value.fieldType === 'SELECT' || fieldForm.value.fieldType === 'CHECKBOX')
        ? fieldForm.value.options.filter(opt => opt.trim())
        : null,
      config: fieldForm.value.config
    }

    if (editingField.value) {
      // 更新字段
      try {
        const updatedField = await updateGameFormField(editingField.value.id, fieldData)

        // 更新本地数据
        const index = gameFormFields.value.findIndex(f => f.id === editingField.value.id)
        if (index !== -1) {
          gameFormFields.value[index] = updatedField
        }

        ElMessage.success('字段更新成功')
      } catch (error) {
        console.error('更新字段失败:', error)
        ElMessage.error('字段更新失败')
        return
      }
    } else {
      // 创建新字段
      await createGameFormField(fieldData)
      ElMessage.success('字段添加成功')
    }

    showFieldConfigDialog.value = false
    resetFieldForm()

    // 重新加载字段列表
    if (selectedGame.value) {
      await loadGameFormFields(selectedGame.value.id)
    }
  } catch (error) {
    console.error('保存字段失败:', error)
    ElMessage.error('保存字段失败')
  } finally {
    saving.value = false
  }
}

// 图片上传相关方法
const handleImageUpload = (response: any, fieldKey: string) => {
  if (response && response.url) {
    formData.value[fieldKey] = response.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

const beforeImageUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

onMounted(() => {
  loadActiveGames()
})
</script>

<style scoped>
.create-order-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.create-order-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 游戏选择样式 */
.game-selection {
  padding: 20px;
}

.section-title {
  margin-bottom: 20px;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.game-card {
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.game-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.game-content {
  text-align: center;
}

.game-content h4 {
  margin: 12px 0 8px 0;
  color: #303133;
}

.game-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 表单区域样式 */
.order-form-container {
  padding: 20px;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 24px;
}

.game-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.game-details h3 {
  margin: 0 0 4px 0;
  color: #303133;
}

.game-details p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 改进的双栏布局 */
.improved-layout {
  margin-top: 20px;
}

.layout-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  min-height: 500px;
}

/* 左侧表单区域 */
.form-section {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.section-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.compact-form {
  padding: 20px;
}

.compact-form-item {
  margin-bottom: 20px;
}

.compact-form-item .el-form-item__label {
  font-weight: 500;
  color: #606266;
}

/* 右侧管理区域 */
.management-section {
  background: #ffffff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  height: fit-content;
}

.field-list {
  padding: 16px;
  max-height: 600px;
  overflow-y: auto;
}

/* 拖拽字段项 */
.draggable-container {
  min-height: 100px;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  cursor: move;
}

.field-item:hover {
  background: #ecf5ff;
  border-color: #b3d8ff;
}

.field-drag-handle {
  color: #909399;
  cursor: grab;
}

.field-drag-handle:active {
  cursor: grabbing;
}

.field-info {
  flex: 1;
  min-width: 0;
}

.field-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.field-name {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.field-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-key {
  font-size: 12px;
  color: #909399;
  font-family: monospace;
}

.field-actions {
  display: flex;
  gap: 4px;
}

/* 拖拽状态样式 */
.ghost-item {
  opacity: 0.5;
  background: #e6f7ff;
}

.chosen-item {
  background: #e6f7ff;
  border-color: #1890ff;
}

/* 无字段提示 */
.no-fields-tip {
  text-align: center;
  padding: 40px 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .layout-container {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .management-section {
    order: -1;
  }
}

@media (max-width: 768px) {
  .create-order-container {
    padding: 12px;
  }

  .form-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .compact-form {
    padding: 16px;
  }

  .field-item {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .field-title {
    justify-content: space-between;
  }
}

/* 字段配置对话框样式 */
.options-config {
  width: 100%;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.option-item:last-child {
  margin-bottom: 0;
}

.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 8px;
}

/* 图片上传样式 */
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-uploader:hover {
  border-color: #409eff;
}

.uploaded-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  display: block;
}

.upload-placeholder {
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.upload-placeholder:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 8px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
}
</style>
