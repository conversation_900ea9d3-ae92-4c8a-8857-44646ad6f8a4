<template>
  <div class="order-details-container">
    <div class="page-header">
      <h1>订单详情</h1>
      <el-button @click="$router.go(-1)">返回</el-button>
    </div>

    <el-card v-loading="loading" class="order-card">
      <template #header>
        <div class="order-header">
          <div class="order-info">
            <h2>订单号：{{ order?.orderNo }}</h2>
            <el-tag :type="getStatusType(order?.status)">
              {{ getStatusText(order?.status) }}
            </el-tag>
          </div>
          <div class="order-meta">
            <span>游戏：{{ order?.gameType }}</span>
            <span>创建时间：{{ formatDate(order?.createdAt) }}</span>
          </div>
        </div>
      </template>

      <!-- 动态表单数据展示 - 按照新思路文档要求 -->
      <div v-if="formFields.length > 0" class="dynamic-form-display">
        <h3>订单详情</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item
            v-for="field in formFields"
            :key="field.fieldKey"
            :label="field.fieldLabel"
            :span="getFieldSpan(field.fieldType)"
          >
            <div class="field-value">
              <!-- 根据字段类型格式化显示 -->
              <template v-if="field.fieldType === 'IMAGE'">
                <el-image
                  v-if="getFieldValue(field.fieldKey)"
                  :src="getFieldValue(field.fieldKey)"
                  :preview-src-list="[getFieldValue(field.fieldKey)]"
                  fit="cover"
                  style="width: 100px; height: 100px;"
                />
                <span v-else class="empty-value">未上传</span>
              </template>
              
              <template v-else-if="field.fieldType === 'PASSWORD'">
                <span class="password-value">******</span>
                <el-button
                  size="small"
                  type="text"
                  @click="togglePasswordVisibility(field.fieldKey)"
                >
                  {{ passwordVisible[field.fieldKey] ? '隐藏' : '显示' }}
                </el-button>
                <span v-if="passwordVisible[field.fieldKey]" class="password-text">
                  {{ getFieldValue(field.fieldKey) }}
                </span>
              </template>
              
              <template v-else-if="field.fieldType === 'CHECKBOX'">
                <el-tag
                  v-for="option in getCheckboxValues(field.fieldKey)"
                  :key="option"
                  size="small"
                  style="margin-right: 8px;"
                >
                  {{ option }}
                </el-tag>
                <span v-if="!getCheckboxValues(field.fieldKey).length" class="empty-value">
                  未选择
                </span>
              </template>
              
              <template v-else-if="field.fieldType === 'TEXTAREA'">
                <div class="textarea-value">
                  {{ getFieldValue(field.fieldKey) || '未填写' }}
                </div>
              </template>
              
              <template v-else>
                <span :class="{ 'empty-value': !getFieldValue(field.fieldKey) }">
                  {{ getFieldValue(field.fieldKey) || '未填写' }}
                </span>
              </template>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 基础订单信息 -->
      <div class="basic-info">
        <h3>基础信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单价格">
            <span class="price">¥{{ order?.price || 0 }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="客户姓名">
            {{ order?.customerName || '未填写' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系方式">
            {{ order?.customerContact || '未填写' }}
          </el-descriptions-item>
          <el-descriptions-item label="游戏账号">
            {{ order?.gameAccount || '未填写' }}
          </el-descriptions-item>
          <el-descriptions-item label="分配员工" :span="2">
            {{ order?.assignedEmployee?.username || '未分配' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons" v-if="canOperate">
        <el-button
          v-if="order?.status === 'PENDING'"
          type="primary"
          @click="handleAcceptOrder"
          :loading="operating"
        >
          接受订单
        </el-button>
        <el-button
          v-if="order?.status === 'IN_PROGRESS'"
          type="success"
          @click="handleCompleteOrder"
          :loading="operating"
        >
          完成订单
        </el-button>
        <el-button
          v-if="['PENDING', 'IN_PROGRESS'].includes(order?.status)"
          type="warning"
          @click="handleUpdateProgress"
        >
          更新进度
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { orderApi } from '@/api/orders';
import { getActiveFieldsByGameId } from '@/api/gameFormFields';
import { useAuthStore } from '@/stores/auth';
import type { Order } from '@/types/order';
import type { GameFormField } from '@/api/gameFormFields';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

// 响应式数据
const loading = ref(false);
const operating = ref(false);
const order = ref<Order | null>(null);
const formFields = ref<GameFormField[]>([]);
const passwordVisible = ref<Record<string, boolean>>({});

// 计算属性
const canOperate = computed(() => {
  return authStore.user?.role === 'EMPLOYEE' && 
         order.value?.assignedEmployeeId === authStore.user?.id;
});

// 方法
const loadOrderDetails = async () => {
  const orderId = route.params.id as string;
  if (!orderId) return;

  try {
    loading.value = true;
    
    // 获取订单详情
    const orderResponse = await orderApi.getOrderById(orderId);
    order.value = orderResponse.data;

    // 根据游戏ID获取表单字段配置（新思路核心实现）
    if (order.value?.gameId) {
      formFields.value = await getActiveFieldsByGameId(order.value.gameId);
    }
  } catch (error) {
    console.error('加载订单详情失败:', error);
    ElMessage.error('加载订单详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取字段值（从details JSON中获取）
const getFieldValue = (fieldKey: string) => {
  if (!order.value?.details) return '';
  return order.value.details[fieldKey] || '';
};

// 获取多选框值
const getCheckboxValues = (fieldKey: string) => {
  const value = getFieldValue(fieldKey);
  if (Array.isArray(value)) return value;
  if (typeof value === 'string' && value) return [value];
  return [];
};

// 获取字段跨度
const getFieldSpan = (fieldType: string) => {
  return fieldType === 'TEXTAREA' ? 2 : 1;
};

// 切换密码显示
const togglePasswordVisibility = (fieldKey: string) => {
  passwordVisible.value[fieldKey] = !passwordVisible.value[fieldKey];
};

// 格式化日期
const formatDate = (dateString?: string) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleString('zh-CN');
};

// 获取状态类型
const getStatusType = (status?: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': 'warning',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  };
  return statusMap[status || ''] || 'info';
};

// 获取状态文本
const getStatusText = (status?: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': '待处理',
    'IN_PROGRESS': '进行中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  };
  return statusMap[status || ''] || '未知';
};

// 接受订单
const handleAcceptOrder = async () => {
  try {
    await ElMessageBox.confirm('确定要接受这个订单吗？', '确认接受', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    });

    operating.value = true;
    await orderApi.updateOrderStatus(order.value!.id, 'IN_PROGRESS');
    ElMessage.success('订单接受成功');
    await loadOrderDetails(); // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error('接受订单失败:', error);
      ElMessage.error('接受订单失败');
    }
  } finally {
    operating.value = false;
  }
};

// 完成订单
const handleCompleteOrder = async () => {
  try {
    await ElMessageBox.confirm('确定要完成这个订单吗？', '确认完成', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    });

    operating.value = true;
    await orderApi.updateOrderStatus(order.value!.id, 'COMPLETED');
    ElMessage.success('订单完成成功');
    await loadOrderDetails(); // 重新加载数据
  } catch (error) {
    if (error !== 'cancel') {
      console.error('完成订单失败:', error);
      ElMessage.error('完成订单失败');
    }
  } finally {
    operating.value = false;
  }
};

// 更新进度
const handleUpdateProgress = () => {
  ElMessage.info('进度更新功能开发中');
};

// 生命周期
onMounted(() => {
  loadOrderDetails();
});
</script>

<style lang="scss" scoped>
.order-details-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
  }
}

.order-card {
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .order-info {
      display: flex;
      align-items: center;
      gap: 12px;

      h2 {
        margin: 0;
        font-size: 18px;
      }
    }

    .order-meta {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
}

.dynamic-form-display,
.basic-info {
  margin-bottom: 24px;

  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 500;
  }
}

.field-value {
  .empty-value {
    color: var(--el-text-color-placeholder);
    font-style: italic;
  }

  .password-value {
    color: var(--el-text-color-regular);
  }

  .password-text {
    margin-left: 8px;
    font-family: monospace;
  }

  .textarea-value {
    white-space: pre-wrap;
    line-height: 1.5;
  }
}

.price {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.action-buttons {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--el-border-color-light);
  display: flex;
  gap: 12px;
}
</style>
