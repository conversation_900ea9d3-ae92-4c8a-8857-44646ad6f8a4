<template>
  <div class="orders-container">
    <div class="page-header">
      <h1>订单管理</h1>
      <el-button
        type="primary"
        @click="$router.push('/boss/orders/create')"
        v-permission="['BOSS', 'ADMIN']"
      >
        <el-icon><Plus /></el-icon>
        创建订单
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="订单号、客户姓名、游戏账号"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="全部状态"
            clearable
            style="width: 120px"
          >
            <el-option label="待处理" value="PENDING" />
            <el-option label="已分配" value="ASSIGNED" />
            <el-option label="进行中" value="IN_PROGRESS" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select
            v-model="searchForm.priority"
            placeholder="全部优先级"
            clearable
            style="width: 120px"
          >
            <el-option label="低" value="LOW" />
            <el-option label="普通" value="NORMAL" />
            <el-option label="高" value="HIGH" />
            <el-option label="紧急" value="URGENT" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="table-card">
      <el-table
        :data="orders"
        v-loading="loading"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="orderNo" label="订单号" width="120" />
        <el-table-column prop="customerName" label="客户姓名" width="100" />
        <el-table-column label="游戏类型" width="120">
          <template #default="{ row }">
            <el-tag type="primary" size="small" v-if="row.gameType">
              {{ getGameTypeName(row.gameType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="价格" width="100">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)" size="small">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="150">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">查看</el-button>
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(row)"
              v-permission="['BOSS', 'ADMIN']"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(row)"
              v-permission="['BOSS', 'ADMIN']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 订单详情对话框 -->
    <OrderDetailDialog
      v-model="showDetailDialog"
      :order-id="selectedOrderId"
    />

    <!-- 编辑订单对话框 -->
    <OrderEditDialog
      v-model="showEditDialog"
      :order-id="selectedOrderId"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
// 使用auto-import，不需要手动导入Vue的组合式API
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { orderApi, type OrderQuery } from '@/api/orders'
import type { Order, OrderStatus, OrderPriority } from '@/types'
import OrderDetailDialog from '@/components/OrderDetailDialog.vue'
import OrderEditDialog from '@/components/OrderEditDialog.vue'

// 日期格式化函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 游戏类型名称映射
const getGameTypeName = (gameType: string) => {
  const gameTypeMap: Record<string, string> = {
    'wzry': '王者荣耀',
    'lol': '英雄联盟',
    'ys': '原神',
    'mc': '鸣潮',
    'valorant': 'Valorant',
    'csgo': 'CS:GO',
    'dota2': 'Dota2',
    'overwatch': '守望先锋',
    'apex': 'Apex英雄',
    'pubg': '绝地求生',
    'genshin': '原神',
    'honkai': '崩坏：星穹铁道'
  }
  return gameTypeMap[gameType] || gameType
}

// 响应式数据
const loading = ref(false)
const orders = ref<Order[]>([])
const showDetailDialog = ref(false)
const showEditDialog = ref(false)
const selectedOrderId = ref<string | null>(null)
const pagination = reactive({
  page: 1,
  limit: 10,
  total: 0
})

const searchForm = reactive<OrderQuery>({
  keyword: '',
  status: undefined,
  priority: undefined
})

// 获取订单列表
const fetchOrders = async () => {
  try {
    loading.value = true
    const response = await orderApi.getOrders({
      ...searchForm,
      page: pagination.page,
      limit: pagination.limit
    })

    if (response.success && response.data) {
      orders.value = response.data.items
      pagination.total = response.data.pagination.total
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchOrders()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined,
    priority: undefined
  })
  pagination.page = 1
  fetchOrders()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchOrders()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchOrders()
}

// 查看订单
const handleView = (order: Order) => {
  selectedOrderId.value = order.id
  showDetailDialog.value = true
}

// 编辑订单
const handleEdit = (order: Order) => {
  selectedOrderId.value = order.id
  showEditDialog.value = true
}

// 编辑成功回调
const handleEditSuccess = () => {
  fetchOrders()
}

// 删除订单
const handleDelete = async (order: Order) => {
  try {
    // 根据订单状态提供不同的确认信息
    let confirmMessage = `确定要删除订单 ${order.orderNo} 吗？`

    // 根据订单状态提供不同的警告信息
    if (order.status === 'IN_PROGRESS') {
      confirmMessage += '\n注意：进行中的订单无法删除，请先取消订单。'
    } else if (order.status === 'ASSIGNED') {
      confirmMessage += '\n注意：已分配的订单可能有关联任务，删除前请确认。'
    }

    await ElMessageBox.confirm(
      confirmMessage,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false
      }
    )

    // 显示加载状态
    const loadingInstance = ElMessage({
      type: 'info',
      message: '正在删除订单...',
      duration: 0
    })

    try {
      const response = await orderApi.deleteOrder(order.id)
      // 关闭加载提示
      loadingInstance.close()

      if (response.success) {
        ElMessage.success('删除成功')
        fetchOrders() // 重新加载订单列表
      }
    } catch (apiError: any) {
      // 关闭加载提示
      loadingInstance.close()

      // 处理具体错误
      console.error('删除订单失败:', apiError)

      // 根据错误类型显示不同的错误信息
      if (apiError.status === 403) {
        ElMessage.error(apiError.message || '您没有权限删除此订单')
      } else if (apiError.status === 400) {
        ElMessage.error(apiError.message || '订单存在关联数据，无法删除')
      } else if (apiError.status === 404) {
        ElMessage.error('订单不存在或已被删除')
        // 刷新列表，因为订单可能已被其他用户删除
        fetchOrders()
      } else {
        ElMessage.error(apiError.message || '删除订单失败，请稍后重试')
      }
    }
  } catch (error: any) {
    // 用户取消操作
    if (error === 'cancel') {
      return
    }

    // 其他错误
    console.error('删除订单操作失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 状态相关方法
const getStatusType = (status: OrderStatus) => {
  const statusMap = {
    PENDING: 'warning',
    ASSIGNED: 'info',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: OrderStatus) => {
  const statusMap = {
    PENDING: '待处理',
    ASSIGNED: '已分配',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

const getPriorityType = (priority: OrderPriority) => {
  const priorityMap = {
    LOW: 'info',
    NORMAL: 'primary',
    HIGH: 'warning',
    URGENT: 'danger'
  }
  return priorityMap[priority] || 'primary'
}

const getPriorityText = (priority: OrderPriority) => {
  const priorityMap = {
    LOW: '低',
    NORMAL: '普通',
    HIGH: '高',
    URGENT: '紧急'
  }
  return priorityMap[priority] || priority
}

// 初始化
onMounted(() => {
  fetchOrders()
})
</script>

<style lang="scss" scoped>
.orders-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    color: var(--text-primary);
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
