import { Request, Response, NextFunction } from 'express';
import { UserRole } from '@prisma/client';
import { ApiResponse } from '../types/common';

// 角色权限中间件
export const requireRole = (allowedRoles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 检查用户是否已认证
    if (!req.user) {
      const response: ApiResponse = {
        success: false,
        message: '未认证用户',
        timestamp: new Date().toISOString(),
      };

      res.status(401).json(response);
      return;
    }

    // 检查用户角色是否在允许的角色列表中
    if (!allowedRoles.includes(req.user.role)) {
      const response: ApiResponse = {
        success: false,
        message: '权限不足',
        timestamp: new Date().toISOString(),
      };

      res.status(403).json(response);
      return;
    }

    // 权限检查通过，继续执行下一个中间件
    next();
  };
};

// 检查是否为管理员（ADMIN或BOSS）
export const requireAdmin = requireRole([UserRole.ADMIN, UserRole.BOSS]);

// 检查是否为老板
export const requireBoss = requireRole([UserRole.BOSS]);

// 检查是否为员工或以上权限
export const requireEmployee = requireRole([UserRole.EMPLOYEE, UserRole.BOSS, UserRole.ADMIN]);
