<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑游戏' : '添加游戏'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="游戏名称" prop="name">
        <el-input 
          v-model="form.name" 
          placeholder="请输入游戏英文名称（如：wzry）"
          :disabled="isEdit"
        />
        <div class="form-tip">游戏的唯一标识符，创建后不可修改</div>
      </el-form-item>

      <el-form-item label="显示名称" prop="displayName">
        <el-input 
          v-model="form.displayName" 
          placeholder="请输入游戏显示名称（如：王者荣耀）"
        />
      </el-form-item>

      <el-form-item label="游戏描述" prop="description">
        <el-input 
          v-model="form.description" 
          type="textarea"
          :rows="3"
          placeholder="请输入游戏描述"
        />
      </el-form-item>

      <el-form-item label="游戏图标" prop="icon">
        <el-input 
          v-model="form.icon" 
          placeholder="请输入游戏图标URL"
        />
        <div v-if="form.icon" class="icon-preview">
          <img :src="form.icon" alt="游戏图标" />
        </div>
      </el-form-item>

      <el-form-item label="排序权重" prop="sortOrder">
        <el-input-number 
          v-model="form.sortOrder" 
          :min="0"
          :max="999"
          placeholder="数字越小排序越靠前"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="是否启用" prop="isActive">
        <el-switch v-model="form.isActive" />
      </el-form-item>

      <el-form-item label="游戏配置">
        <el-card class="config-card">
          <el-form-item label="最大段位跨越" label-width="120px">
            <el-input-number 
              v-model="gameConfig.maxRankJump" 
              :min="1"
              :max="10"
              placeholder="允许的最大段位跨越数"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="赛季持续天数" label-width="120px">
            <el-input-number 
              v-model="gameConfig.seasonDuration" 
              :min="30"
              :max="365"
              placeholder="赛季持续天数"
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="游戏特色" label-width="120px">
            <el-select
              v-model="gameConfig.features"
              multiple
              filterable
              allow-create
              placeholder="请选择或输入游戏特色功能"
              style="width: 100%"
            >
              <el-option
                v-for="feature in commonFeatures"
                :key="feature"
                :label="feature"
                :value="feature"
              />
            </el-select>
          </el-form-item>
        </el-card>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { useGameStore } from '@/stores/game';
import type { CreateGameRequest, UpdateGameRequest, GameConfig } from '@/types/game';

interface Props {
  modelValue: boolean;
  gameId?: string;
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void;
  (e: 'success'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const gameStore = useGameStore();
const formRef = ref<FormInstance>();
const loading = ref(false);

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isEdit = computed(() => !!props.gameId);

// 常用游戏特色选项
const commonFeatures = [
  '排位赛',
  '巅峰赛',
  '娱乐模式',
  '云顶之弈',
  '极地大乱斗',
  '创意工坊',
  '竞技模式',
  '经典模式'
];

// 表单数据
const form = reactive<CreateGameRequest>({
  name: '',
  displayName: '',
  description: '',
  icon: '',
  sortOrder: 0,
  isActive: true
});

// 游戏配置
const gameConfig = reactive<GameConfig>({
  maxRankJump: 2,
  seasonDuration: 90,
  features: []
});

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入游戏名称', trigger: 'blur' },
    { min: 2, max: 20, message: '游戏名称长度在 2 到 20 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '游戏名称只能包含字母、数字、下划线和连字符', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' },
    { min: 2, max: 50, message: '显示名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过 500 个字符', trigger: 'blur' }
  ],
  icon: [
    { type: 'url', message: '请输入有效的URL地址', trigger: 'blur' }
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 999, message: '排序权重必须在 0 到 999 之间', trigger: 'blur' }
  ]
};

// 方法
const resetForm = () => {
  Object.assign(form, {
    name: '',
    displayName: '',
    description: '',
    icon: '',
    sortOrder: 0,
    isActive: true
  });
  
  Object.assign(gameConfig, {
    maxRankJump: 2,
    seasonDuration: 90,
    features: []
  });
  
  formRef.value?.clearValidate();
};

const loadGameData = async () => {
  if (!props.gameId) return;
  
  try {
    loading.value = true;
    const game = await gameStore.fetchGameById(props.gameId);
    
    Object.assign(form, {
      name: game.name,
      displayName: game.displayName,
      description: game.description || '',
      icon: game.icon || '',
      sortOrder: game.sortOrder,
      isActive: game.isActive
    });
    
    if (game.config) {
      Object.assign(gameConfig, {
        maxRankJump: game.config.maxRankJump || 2,
        seasonDuration: game.config.seasonDuration || 90,
        features: game.config.features || []
      });
    }
  } catch (error) {
    console.error('加载游戏数据失败:', error);
    ElMessage.error('加载游戏数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  
  try {
    const valid = await formRef.value.validate();
    if (!valid) return;
    
    loading.value = true;
    
    const submitData = {
      ...form,
      config: gameConfig
    };
    
    if (isEdit.value) {
      await gameStore.updateGame(props.gameId!, submitData as UpdateGameRequest);
      ElMessage.success('游戏更新成功');
    } else {
      await gameStore.createGame(submitData);
      ElMessage.success('游戏创建成功');
    }
    
    emit('success');
  } catch (error) {
    console.error('保存游戏失败:', error);
    ElMessage.error('保存游戏失败');
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    if (isEdit.value) {
      loadGameData();
    } else {
      resetForm();
    }
  }
});
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}

.icon-preview {
  margin-top: 8px;
  
  img {
    width: 48px;
    height: 48px;
    object-fit: contain;
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    padding: 4px;
  }
}

.config-card {
  width: 100%;
  
  :deep(.el-card__body) {
    padding: 16px;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
