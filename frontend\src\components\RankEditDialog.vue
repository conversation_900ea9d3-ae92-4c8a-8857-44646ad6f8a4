<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑段位' : '添加段位'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="段位名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="如: bronze_1"
              :disabled="isEdit"
            />
            <div class="form-tip">
              程序内部使用的唯一标识，只能包含字母、数字和下划线
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="显示名称" prop="displayName">
            <el-input
              v-model="formData.displayName"
              placeholder="如: 青铜I"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="段位等级" prop="level">
            <el-input-number
              v-model="formData.level"
              :min="1"
              :max="100"
              placeholder="数字越大等级越高"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="难度系数" prop="difficultyMultiplier">
            <el-input-number
              v-model="formData.difficultyMultiplier"
              :min="0.1"
              :max="10"
              :step="0.1"
              :precision="1"
              placeholder="影响价格计算"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="段位图标" prop="icon">
        <el-input
          v-model="formData.icon"
          placeholder="图标URL地址"
        />
      </el-form-item>

      <el-form-item label="段位描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="段位描述信息"
        />
      </el-form-item>

      <el-form-item label="启用状态">
        <el-switch
          v-model="formData.isActive"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useGameStore } from '../stores/game';
import type { GameRank, CreateGameRankRequest, UpdateGameRankRequest } from '../types/game';

export default defineComponent({
  name: 'RankEditDialog',
  props: {
    modelValue: {
      type: Boolean,
      required: true
    },
    gameId: {
      type: String,
      required: true
    },
    rankId: {
      type: String,
      default: ''
    }
  },
  emits: ['update:modelValue', 'success'],
  setup(props, { emit }) {

const gameStore = useGameStore();

// 响应式数据
const loading = ref(false);
const formRef = ref();
const currentRank = ref<GameRank | null>(null);

// 表单数据
const formData = reactive({
  name: '',
  displayName: '',
  level: 1,
  difficultyMultiplier: 1.0,
  icon: '',
  description: '',
  isActive: true
});

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const isEdit = computed(() => !!props.rankId);

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入段位名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '段位名称只能包含字母、数字和下划线，且必须以字母开头', trigger: 'blur' }
  ],
  displayName: [
    { required: true, message: '请输入显示名称', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请输入段位等级', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '段位等级必须在1-100之间', trigger: 'blur' }
  ],
  difficultyMultiplier: [
    { required: true, message: '请输入难度系数', trigger: 'blur' },
    { type: 'number', min: 0.1, max: 10, message: '难度系数必须在0.1-10之间', trigger: 'blur' }
  ]
};

// 方法
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    displayName: '',
    level: 1,
    difficultyMultiplier: 1.0,
    icon: '',
    description: '',
    isActive: true
  });
  formRef.value?.clearValidate();
};

const loadRankData = async () => {
  if (!props.rankId) return;
  
  try {
    loading.value = true;
    const rank = await gameStore.fetchGameRankById(props.rankId);
    currentRank.value = rank;
    
    Object.assign(formData, {
      name: rank.name,
      displayName: rank.displayName,
      level: rank.level,
      difficultyMultiplier: rank.difficultyMultiplier,
      icon: rank.icon || '',
      description: rank.description || '',
      isActive: rank.isActive
    });
  } catch (error) {
    console.error('加载段位数据失败:', error);
    ElMessage.error('加载段位数据失败');
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    
    loading.value = true;
    
    if (isEdit.value && props.rankId) {
      // 更新段位
      const updateData: UpdateGameRankRequest = {
        name: formData.name,
        displayName: formData.displayName,
        level: formData.level,
        difficultyMultiplier: formData.difficultyMultiplier,
        icon: formData.icon || undefined,
        description: formData.description || undefined,
        isActive: formData.isActive
      };
      
      await gameStore.updateGameRank(props.rankId, updateData);
      ElMessage.success('段位更新成功');
    } else {
      // 创建段位
      const createData: CreateGameRankRequest = {
        gameId: props.gameId,
        name: formData.name,
        displayName: formData.displayName,
        level: formData.level,
        difficultyMultiplier: formData.difficultyMultiplier,
        icon: formData.icon || undefined,
        description: formData.description || undefined,
        isActive: formData.isActive
      };
      
      await gameStore.createGameRank(createData);
      ElMessage.success('段位创建成功');
    }
    
    emit('success');
  } catch (error) {
    console.error('保存段位失败:', error);
    ElMessage.error('保存段位失败');
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  visible.value = false;
  resetForm();
  currentRank.value = null;
};

// 监听对话框显示状态
watch(visible, (newVal) => {
  if (newVal) {
    if (isEdit.value) {
      loadRankData();
    } else {
      resetForm();
    }
  }
});

return {
  loading,
  formRef,
  currentRank,
  formData,
  visible,
  isEdit,
  formRules,
  resetForm,
  loadRankData,
  handleSubmit,
  handleClose
};
  }
});
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}
</style>
