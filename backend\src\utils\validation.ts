import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { ValidationError } from '../middleware/errorHandler';

// 验证中间件
export function validate(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      throw new ValidationError(errorMessage);
    }

    req.body = value;
    next();
  };
}

// 查询参数验证
export function validateQuery(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      throw new ValidationError(errorMessage);
    }

    req.query = value;
    next();
  };
}

// 路径参数验证
export function validateParams(schema: Joi.ObjectSchema) {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      throw new ValidationError(errorMessage);
    }

    req.params = value;
    next();
  };
}

// 通用验证规则
export const commonSchemas = {
  // ID验证
  id: Joi.string().required().messages({
    'string.empty': 'ID不能为空',
    'any.required': 'ID是必需的',
  }),

  // 分页参数
  pagination: Joi.object({
    page: Joi.alternatives().try(
      Joi.number().integer().min(1),
      Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
    ).default(1),
    limit: Joi.alternatives().try(
      Joi.number().integer().min(1).max(100),
      Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
    ).default(10),
    sortBy: Joi.string().optional(),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  }),

  // 搜索参数
  search: Joi.object({
    keyword: Joi.string().optional(),
    status: Joi.string().optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
  }),
};

// 认证相关验证
export const authSchemas = {
  login: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required().messages({
      'string.alphanum': '用户名只能包含字母和数字',
      'string.min': '用户名至少3个字符',
      'string.max': '用户名最多30个字符',
      'any.required': '用户名是必需的',
    }),
    password: Joi.string().min(6).required().messages({
      'string.min': '密码至少6个字符',
      'any.required': '密码是必需的',
    }),
  }),

  register: Joi.object({
    username: Joi.string().alphanum().min(3).max(30).required(),
    password: Joi.string().min(6).required(),
    nickname: Joi.string().max(50).optional(),
    phone: Joi.string().pattern(/^1[3-9]\d{9}$/).optional().messages({
      'string.pattern.base': '请输入有效的手机号码',
    }),
    role: Joi.string().valid('ADMIN', 'BOSS', 'EMPLOYEE').optional(),
  }),

  updatePassword: Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': '当前密码是必需的',
    }),
    newPassword: Joi.string().min(6).required().messages({
      'string.min': '新密码至少6个字符',
      'any.required': '新密码是必需的',
    }),
  }),
};

// 游戏类型配置 - 定义哪些游戏需要段位字段
const gameTypeConfigs = {
  // 有段位系统的游戏
  wzry: { hasRanks: true, displayName: '王者荣耀' },
  lol: { hasRanks: true, displayName: '英雄联盟' },
  pubg: { hasRanks: true, displayName: '绝地求生' },

  // 无段位系统的游戏
  ys: { hasRanks: false, displayName: '原神' },
  mc: { hasRanks: false, displayName: '鸣潮' },
  default: { hasRanks: false, displayName: '其他游戏' }
};

// 动态订单验证函数
export function createOrderValidationSchema(gameType?: string) {
  const gameConfig = gameTypeConfigs[gameType as keyof typeof gameTypeConfigs] || gameTypeConfigs.default;

  console.log(`创建订单验证Schema - 游戏类型: ${gameType}, 配置:`, gameConfig);

  const baseSchema = {
    customerName: Joi.string().max(100).required().messages({
      'any.required': '客户姓名是必需的',
    }),
    customerContact: Joi.string().max(50).allow('').optional(),
    gameAccount: Joi.string().max(100).required().messages({
      'any.required': '游戏账号是必需的',
    }),
    gamePassword: Joi.string().max(100).required().messages({
      'any.required': '游戏密码是必需的',
    }),
    price: Joi.number().positive().required().messages({
      'number.positive': '价格必须大于0',
      'any.required': '价格是必需的',
    }),
    gameType: Joi.string().required().messages({
      'any.required': '游戏类型是必需的',
    }),
    deadline: Joi.date().iso().optional(),
    requirements: Joi.string().max(500).allow('').optional(),
    priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').optional().default('NORMAL'),
  };

  // 移除段位字段，不再支持段位相关功能
  console.log(`游戏 ${gameConfig.displayName} - 已移除段位字段`);

  return Joi.object(baseSchema);
}

// 动态订单验证中间件
export function validateOrderCreate(req: Request, res: Response, next: NextFunction) {
  const gameType = req.body.gameType;
  const schema = createOrderValidationSchema(gameType);

  const { error, value } = schema.validate(req.body, {
    abortEarly: false,
    stripUnknown: true,
  });

  if (error) {
    const errorMessage = error.details
      .map(detail => detail.message)
      .join(', ');
    throw new ValidationError(errorMessage);
  }

  req.body = value;
  next();
}

// 订单相关验证（保留原有的静态验证作为备用）
export const orderSchemas = {
  create: Joi.object({
    customerName: Joi.string().max(100).required().messages({
      'any.required': '客户姓名是必需的',
    }),
    customerContact: Joi.string().max(50).allow('').optional(),
    gameAccount: Joi.string().max(100).required().messages({
      'any.required': '游戏账号是必需的',
    }),
    gamePassword: Joi.string().max(100).required().messages({
      'any.required': '游戏密码是必需的',
    }),

    price: Joi.number().positive().required().messages({
      'number.positive': '价格必须大于0',
      'any.required': '价格是必需的',
    }),
    deadline: Joi.date().iso().greater('now').optional().messages({
      'date.greater': '截止时间必须晚于当前时间',
    }),
    requirements: Joi.string().max(500).allow('').optional(),
    priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').default('NORMAL'),
  }),

  update: Joi.object({
    customerName: Joi.string().max(100).optional(),
    customerContact: Joi.string().max(50).optional(),

    price: Joi.number().positive().optional(),
    deadline: Joi.date().iso().optional(),
    requirements: Joi.string().max(500).allow('').optional(),
    priority: Joi.string().valid('LOW', 'NORMAL', 'HIGH', 'URGENT').optional(),
    status: Joi.string().valid('PENDING', 'ASSIGNED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED').optional(),
  }),
};

// 任务相关验证
export const taskSchemas = {
  create: Joi.object({
    orderId: Joi.string().required(),
    assigneeId: Joi.string().optional(),
    assignType: Joi.string().valid('DIRECT', 'SYSTEM').required(),
    estimatedHours: Joi.number().integer().positive().optional(),
    commission: Joi.number().positive().optional(),
    description: Joi.string().max(500).allow('').optional(),
    notes: Joi.string().max(500).allow('').optional(),
  }),

  update: Joi.object({
    assigneeId: Joi.string().optional(),
    status: Joi.string().valid('PENDING', 'ACCEPTED', 'IN_PROGRESS', 'SUBMITTED', 'APPROVED', 'REJECTED', 'COMPLETED', 'CANCELLED').optional(),
    estimatedHours: Joi.number().integer().positive().optional(),
    actualHours: Joi.number().integer().positive().optional(),
    commission: Joi.number().positive().optional(),
    description: Joi.string().max(500).allow('').optional(),
    notes: Joi.string().max(500).allow('').optional(),
  }),

  updateProgress: Joi.object({
    progress: Joi.number().integer().min(0).max(100).required(),
    description: Joi.string().max(500).allow('').optional(),
    screenshots: Joi.array().items(Joi.string()).optional(),
  }),

  review: Joi.object({
    approved: Joi.boolean().required(),
    feedback: Joi.string().max(500).allow('').optional(),
  }),
};
