import { Request, Response } from 'express';
import { onlineUserService, OnlineUserFilters, LoginLogFilters } from '../services/onlineUserService';
import { getSocketService } from '../services/socketService';
import { asyncHandler } from '../middleware/errorHandler';
import { ApiResponse } from '../types/common';
import { UserRole } from '@prisma/client';
import { logger } from '../utils/logger';

// 获取在线用户列表
export const getOnlineUsers = asyncHandler(async (req: Request, res: Response) => {
  const { role, ipAddress, locationProvince, locationCity, minOnlineTime } = req.query;
  
  const filters: OnlineUserFilters = {};
  
  if (role) filters.role = role as UserRole;
  if (ipAddress) filters.ipAddress = ipAddress as string;
  if (locationProvince) filters.locationProvince = locationProvince as string;
  if (locationCity) filters.locationCity = locationCity as string;
  if (minOnlineTime) filters.minOnlineTime = parseInt(minOnlineTime as string);
  
  const onlineUsers = await onlineUserService.getOnlineUsers(filters);
  
  const response: ApiResponse = {
    success: true,
    data: {
      users: onlineUsers,
      total: onlineUsers.length,
      filters
    },
    message: '获取在线用户列表成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取用户会话历史
export const getUserSessions = asyncHandler(async (req: Request, res: Response) => {
  const { userId } = req.params;
  
  const sessions = await onlineUserService.getUserSessions(userId);
  
  const response: ApiResponse = {
    success: true,
    data: sessions,
    message: '获取用户会话历史成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 强制用户下线
export const forceUserLogout = asyncHandler(async (req: Request, res: Response) => {
  const { sessionId } = req.params;
  const { reason } = req.body;

  // 检查权限：只有BOSS和ADMIN可以强制下线
  if (req.user!.role !== UserRole.BOSS && req.user!.role !== UserRole.ADMIN) {
    const response: ApiResponse = {
      success: false,
      message: '权限不足',
      timestamp: new Date().toISOString(),
    };

    res.status(403).json(response);
    return;
  }
  
  // 从数据库层面强制下线
  await onlineUserService.forceLogout(sessionId, reason);
  
  // 从Socket层面强制下线（需要先获取用户ID）
  try {
    const sessions = await onlineUserService.getUserSessions(''); // 这里需要优化
    const session = sessions.find(s => s.id === sessionId);
    if (session) {
      const socketService = getSocketService();
      await socketService.forceUserLogout(session.userId, reason);
    }
  } catch (error) {
    logger.error('Socket层面强制下线失败:', error);
    // 继续执行，不影响数据库层面的操作
  }
  
  const response: ApiResponse = {
    success: true,
    message: '强制下线成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取登录日志
export const getLoginLogs = asyncHandler(async (req: Request, res: Response) => {
  const { 
    userId, 
    username, 
    ipAddress, 
    loginResult, 
    startDate, 
    endDate, 
    locationProvince, 
    locationCity,
    page = '1',
    limit = '20'
  } = req.query;
  
  const filters: LoginLogFilters = {};
  
  if (userId) filters.userId = userId as string;
  if (username) filters.username = username as string;
  if (ipAddress) filters.ipAddress = ipAddress as string;
  if (loginResult) filters.loginResult = loginResult as any;
  if (startDate) filters.startDate = new Date(startDate as string);
  if (endDate) filters.endDate = new Date(endDate as string);
  if (locationProvince) filters.locationProvince = locationProvince as string;
  if (locationCity) filters.locationCity = locationCity as string;
  
  const logs = await onlineUserService.getLoginLogs(filters);
  
  // 简单分页处理
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const startIndex = (pageNum - 1) * limitNum;
  const endIndex = startIndex + limitNum;
  const paginatedLogs = logs.slice(startIndex, endIndex);
  
  const response: ApiResponse = {
    success: true,
    data: {
      logs: paginatedLogs,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: logs.length,
        totalPages: Math.ceil(logs.length / limitNum)
      },
      filters
    },
    message: '获取登录日志成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取在线用户统计
export const getOnlineUserStats = asyncHandler(async (req: Request, res: Response) => {
  const onlineUsers = await onlineUserService.getOnlineUsers();
  
  // 统计数据
  const stats = {
    total: onlineUsers.length,
    byRole: {
      ADMIN: onlineUsers.filter(u => u.role === UserRole.ADMIN).length,
      BOSS: onlineUsers.filter(u => u.role === UserRole.BOSS).length,
      EMPLOYEE: onlineUsers.filter(u => u.role === UserRole.EMPLOYEE).length,
    },
    byLocation: {} as Record<string, number>,
    averageOnlineTime: 0,
    longestOnlineUser: null as any,
  };
  
  // 按地区统计
  onlineUsers.forEach(user => {
    const location = `${user.locationProvince || '未知'} ${user.locationCity || ''}`.trim();
    stats.byLocation[location] = (stats.byLocation[location] || 0) + 1;
  });
  
  // 计算平均在线时长
  if (onlineUsers.length > 0) {
    const totalOnlineTime = onlineUsers.reduce((sum, user) => sum + user.onlineDuration, 0);
    stats.averageOnlineTime = Math.floor(totalOnlineTime / onlineUsers.length);
    
    // 找出在线时间最长的用户
    stats.longestOnlineUser = onlineUsers.reduce((longest, current) => 
      current.onlineDuration > longest.onlineDuration ? current : longest
    );
  }
  
  const response: ApiResponse = {
    success: true,
    data: stats,
    message: '获取在线用户统计成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取登录统计
export const getLoginStats = asyncHandler(async (req: Request, res: Response) => {
  const { days = '7' } = req.query;
  const daysNum = parseInt(days as string);
  
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - daysNum);
  
  const logs = await onlineUserService.getLoginLogs({
    startDate,
    endDate
  });
  
  // 统计数据
  const stats = {
    period: `${daysNum}天`,
    total: logs.length,
    successful: logs.filter(log => log.loginResult === 'SUCCESS').length,
    failed: logs.filter(log => log.loginResult === 'FAILED').length,
    blocked: logs.filter(log => log.loginResult === 'BLOCKED').length,
    uniqueUsers: new Set(logs.map(log => log.userId).filter(Boolean)).size,
    uniqueIPs: new Set(logs.map(log => log.ipAddress)).size,
    byDate: {} as Record<string, number>,
    byHour: {} as Record<string, number>,
    topLocations: {} as Record<string, number>,
    failureReasons: {} as Record<string, number>,
  };
  
  // 按日期统计
  logs.forEach(log => {
    const date = log.loginTime.toISOString().split('T')[0];
    stats.byDate[date] = (stats.byDate[date] || 0) + 1;
    
    const hour = log.loginTime.getHours().toString().padStart(2, '0');
    stats.byHour[hour] = (stats.byHour[hour] || 0) + 1;
    
    const location = `${log.locationProvince || '未知'} ${log.locationCity || ''}`.trim();
    stats.topLocations[location] = (stats.topLocations[location] || 0) + 1;
    
    if (log.failureReason) {
      stats.failureReasons[log.failureReason] = (stats.failureReasons[log.failureReason] || 0) + 1;
    }
  });
  
  const response: ApiResponse = {
    success: true,
    data: stats,
    message: '获取登录统计成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 清理过期会话
export const cleanupExpiredSessions = asyncHandler(async (req: Request, res: Response) => {
  // 检查权限：只有ADMIN可以执行清理操作
  if (req.user!.role !== UserRole.ADMIN) {
    const response: ApiResponse = {
      success: false,
      message: '权限不足',
      timestamp: new Date().toISOString(),
    };

    res.status(403).json(response);
    return;
  }
  
  await onlineUserService.cleanupExpiredSessions();
  
  const response: ApiResponse = {
    success: true,
    message: '清理过期会话成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});

// 获取IP地理位置信息
export const getIpLocation = asyncHandler(async (req: Request, res: Response) => {
  const { ip } = req.params;
  
  // 这里可以调用IP地理位置服务
  // 暂时返回模拟数据
  const response: ApiResponse = {
    success: true,
    data: {
      ip,
      country: '中国',
      province: '广东省',
      city: '深圳市',
      isp: '电信',
    },
    message: '获取IP地理位置成功',
    timestamp: new Date().toISOString(),
  };
  
  res.status(200).json(response);
});
