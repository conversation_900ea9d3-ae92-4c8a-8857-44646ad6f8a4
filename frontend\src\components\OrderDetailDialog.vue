<template>
  <el-dialog
    v-model="visible"
    title="订单详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="order" class="order-detail" v-loading="loading">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusType(order.status)">
              {{ getStatusText(order.status) }}
            </el-tag>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">{{ order.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="客户姓名">{{ order.customerName }}</el-descriptions-item>
          <el-descriptions-item label="客户联系方式">
            {{ order.customerContact || '未提供' }}
          </el-descriptions-item>
          <el-descriptions-item label="游戏类型" v-if="order.gameType">
            <el-tag type="primary" size="small">
              {{ getGameTypeName(order.gameType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="游戏账号">{{ order.gameAccount }}</el-descriptions-item>
          <el-descriptions-item label="订单价格">¥{{ order.price }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(order.priority)" size="small">
              {{ getPriorityText(order.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(order.createdAt) }}</el-descriptions-item>
          <el-descriptions-item label="截止时间">
            {{ order.deadline ? formatDate(order.deadline) : '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="特殊要求" v-if="order.requirements" span="2">
            {{ order.requirements }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 动态字段信息 -->
      <el-card class="info-card" shadow="never" v-if="order.formData && Object.keys(order.formData).length > 0">
        <template #header>
          <span>详细信息</span>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item
            v-for="(value, key) in order.formData"
            :key="key"
            :label="getFieldLabel(key)"
          >
            {{ formatFieldValue(key, value) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 创建者信息 -->
      <el-card class="info-card" shadow="never" v-if="order.createdBy">
        <template #header>
          <span>创建者信息</span>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="创建者">
            {{ order.createdBy.nickname || order.createdBy.username }}
          </el-descriptions-item>
          <el-descriptions-item label="用户名">{{ order.createdBy.username }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 关联任务 -->
      <el-card class="info-card" shadow="never" v-if="order.tasks && order.tasks.length > 0">
        <template #header>
          <span>关联任务</span>
        </template>
        
        <el-table :data="order.tasks" stripe>
          <el-table-column prop="taskNo" label="任务号" width="150" />
          <el-table-column label="分配员工" width="120">
            <template #default="{ row }">
              {{ row.assignee ? (row.assignee.nickname || row.assignee.username) : '未分配' }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getTaskStatusType(row.status)" size="small">
                {{ getTaskStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="分配方式" width="100">
            <template #default="{ row }">
              <el-tag :type="row.assignType === 'DIRECT' ? 'success' : 'info'" size="small">
                {{ row.assignType === 'DIRECT' ? '直接分配' : '系统挂单' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="佣金" width="100">
            <template #default="{ row }">
              {{ row.commission ? `¥${row.commission}` : '未设置' }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="150">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { orderApi } from '@/api/orders'
import type { Order, OrderStatus, OrderPriority, TaskStatus } from '@/types'
import { formatDate } from '@/utils/date'

// Props
interface Props {
  modelValue: boolean
  orderId: string | null
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = ref(false)
const order = ref<Order | null>(null)
const loading = ref(false)

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.orderId) {
    fetchOrderDetail()
  }
})

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!props.orderId) return
  
  try {
    loading.value = true
    const response = await orderApi.getOrderById(props.orderId)
    
    if (response.success && response.data) {
      order.value = response.data
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  order.value = null
}

// 状态相关方法
const getStatusType = (status: OrderStatus) => {
  const statusMap = {
    PENDING: 'warning',
    ASSIGNED: 'info',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: OrderStatus) => {
  const statusMap = {
    PENDING: '待处理',
    ASSIGNED: '已分配',
    IN_PROGRESS: '进行中',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

const getPriorityType = (priority: OrderPriority) => {
  const priorityMap = {
    LOW: 'info',
    NORMAL: 'primary',
    HIGH: 'warning',
    URGENT: 'danger'
  }
  return priorityMap[priority] || 'primary'
}

const getPriorityText = (priority: OrderPriority) => {
  const priorityMap = {
    LOW: '低',
    NORMAL: '普通',
    HIGH: '高',
    URGENT: '紧急'
  }
  return priorityMap[priority] || priority
}

const getTaskStatusType = (status: TaskStatus) => {
  const statusMap = {
    PENDING: 'warning',
    ASSIGNED: 'info',
    IN_PROGRESS: 'primary',
    SUBMITTED: 'success',
    COMPLETED: 'success',
    CANCELLED: 'danger'
  }
  return statusMap[status] || 'info'
}

const getTaskStatusText = (status: TaskStatus) => {
  const statusMap = {
    PENDING: '待分配',
    ASSIGNED: '已分配',
    IN_PROGRESS: '进行中',
    SUBMITTED: '已提交',
    COMPLETED: '已完成',
    CANCELLED: '已取消'
  }
  return statusMap[status] || status
}

const getGameTypeName = (gameType: string) => {
  const gameTypeMap: Record<string, string> = {
    'wzry': '王者荣耀',
    'lol': '英雄联盟',
    'ys': '原神',
    'mc': '鸣潮',
    'valorant': 'Valorant',
    'csgo': 'CS:GO',
    'dota2': 'Dota2',
    'overwatch': '守望先锋',
    'apex': 'Apex英雄',
    'pubg': '绝地求生',
    'genshin': '原神',
    'honkai': '崩坏：星穹铁道'
  }
  return gameTypeMap[gameType] || gameType
}

// 字段标签映射
const fieldLabelMap: Record<string, string> = {
  'customerName': '客户姓名',
  'customerContact': '联系方式',
  'gameAccount': '游戏账号',
  'gamePassword': '游戏密码',
  'currentLevel': '当前等级',
  'targetLevel': '目标等级',
  'serviceType': '服务类型',
  'dailyCheckin': '每日签到',
  'currentRank': '当前段位',
  'targetRank': '目标段位',
  'price': '订单价格',
  'requirements': '特殊要求'
}

// 服务类型映射
const serviceTypeMap: Record<string, string> = {
  'level_boost': '等级提升',
  'quest_complete': '任务完成',
  'resource_farm': '资源收集',
  'daily_tasks': '日常任务'
}

// 获取字段标签
const getFieldLabel = (fieldName: string): string => {
  return fieldLabelMap[fieldName] || fieldName
}

// 格式化字段值
const formatFieldValue = (fieldName: string, value: any): string => {
  if (value === null || value === undefined) {
    return '未设置'
  }

  // 布尔值处理
  if (typeof value === 'boolean') {
    return value ? '是' : '否'
  }

  // 服务类型处理
  if (fieldName === 'serviceType' && typeof value === 'string') {
    return serviceTypeMap[value] || value
  }

  // 价格处理
  if (fieldName === 'price' && typeof value === 'number') {
    return `¥${value}`
  }

  // 数组处理
  if (Array.isArray(value)) {
    return value.join(', ')
  }

  return String(value)
}
</script>

<style lang="scss" scoped>
.order-detail {
  .info-card {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
