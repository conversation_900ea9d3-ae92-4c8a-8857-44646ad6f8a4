import { prisma } from '../config/database';
import { NotFoundError, ValidationError } from '../middleware/errorHandler';
import { PaginationQuery, PaginatedResponse } from '../types/common';
import {
  CreateGameRequest,
  UpdateGameRequest,
  GameQuery,
  GameStatistics,
  GameHotness,
  PriceCalculationResult
} from '../types/game';

export class GameService {
  // 创建游戏
  async createGame(gameData: CreateGameRequest) {
    // 检查游戏名称是否已存在
    const existingGame = await prisma.game.findUnique({
      where: { name: gameData.name }
    });

    if (existingGame) {
      throw new ValidationError('游戏名称已存在');
    }

    const game = await prisma.game.create({
      data: gameData,
      include: {
        ranks: {
          orderBy: { level: 'asc' }
        },
        priceRules: {
          where: { isActive: true }
        },
        _count: {
          select: {
            orders: true,
            employeeSkills: true,
            formFields: true
          }
        }
      }
    });

    return game;
  }

  // 获取游戏列表
  async getGames(query: PaginationQuery & GameQuery) {
    const {
      page = 1,
      limit = 10,
      sortBy = 'sortOrder',
      sortOrder = 'asc',
      isActive
    } = query;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};
    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    // 查询游戏
    const [games, total] = await Promise.all([
      prisma.game.findMany({
        where,
        include: {
          ranks: {
            where: { isActive: true },
            orderBy: { level: 'asc' }
          },
          priceRules: {
            where: { isActive: true }
          },
          _count: {
            select: {
              orders: true,
              employeeSkills: true,
              formFields: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: {
          [sortBy]: sortOrder
        }
      }),
      prisma.game.count({ where })
    ]);

    const totalPages = Math.ceil(total / limit);

    const result: PaginatedResponse<typeof games[0]> = {
      items: games,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };

    return result;
  }

  // 获取游戏详情
  async getGameById(id: string) {
    const game = await prisma.game.findUnique({
      where: { id },
      include: {
        ranks: {
          orderBy: { level: 'asc' }
        },
        priceRules: {
          where: { isActive: true }
        },
        _count: {
          select: {
            orders: true,
            employeeSkills: true,
            formFields: true
          }
        }
      }
    });

    if (!game) {
      throw new NotFoundError('游戏不存在');
    }

    return game;
  }

  // 更新游戏
  async updateGame(id: string, updateData: UpdateGameRequest) {
    // 检查游戏是否存在
    const existingGame = await prisma.game.findUnique({
      where: { id }
    });

    if (!existingGame) {
      throw new NotFoundError('游戏不存在');
    }

    // 如果更新名称，检查是否与其他游戏冲突
    if (updateData.name && updateData.name !== existingGame.name) {
      const nameConflict = await prisma.game.findUnique({
        where: { name: updateData.name }
      });

      if (nameConflict) {
        throw new ValidationError('游戏名称已存在');
      }
    }

    const game = await prisma.game.update({
      where: { id },
      data: updateData,
      include: {
        ranks: {
          orderBy: { level: 'asc' }
        },
        priceRules: {
          where: { isActive: true }
        },
        _count: {
          select: {
            orders: true,
            employeeSkills: true,
            formFields: true
          }
        }
      }
    });

    return game;
  }

  // 获取游戏及其表单字段
  async getGameWithFormFields(id: string) {
    const game = await prisma.game.findUnique({
      where: { id },
      include: {
        formFields: {
          where: { isActive: true },
          orderBy: [
            { sortOrder: 'asc' },
            { createdAt: 'asc' }
          ]
        },
        ranks: {
          orderBy: { level: 'asc' }
        },
        _count: {
          select: {
            orders: true,
            employeeSkills: true,
            formFields: true
          }
        }
      }
    });

    if (!game) {
      throw new NotFoundError('游戏不存在');
    }

    return game;
  }

  // 删除游戏
  async deleteGame(id: string) {
    // 检查游戏是否存在
    const existingGame = await prisma.game.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    });

    if (!existingGame) {
      throw new NotFoundError('游戏不存在');
    }

    // 检查是否有关联的订单
    if (existingGame._count.orders > 0) {
      throw new ValidationError('无法删除有关联订单的游戏，请先处理相关订单');
    }

    await prisma.game.delete({
      where: { id }
    });

    return { message: '游戏删除成功' };
  }

  // 通过名称删除游戏
  async deleteGameByName(name: string) {
    // 检查游戏是否存在
    const existingGame = await prisma.game.findUnique({
      where: { name },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    });

    if (!existingGame) {
      throw new NotFoundError('游戏不存在');
    }

    // 检查是否有关联的订单
    if (existingGame._count.orders > 0) {
      throw new ValidationError('无法删除有关联订单的游戏，请先处理相关订单');
    }

    await prisma.game.delete({
      where: { name }
    });

    return { message: '游戏删除成功' };
  }

  // 获取游戏统计信息
  async getGameStatistics(gameId: string): Promise<GameStatistics> {
    const game = await this.getGameById(gameId);

    // 获取订单统计
    const orderStats = await prisma.order.aggregate({
      where: { gameId },
      _count: true,
      _sum: { price: true },
      _avg: { price: true }
    });

    // 获取完成率
    const completedOrders = await prisma.order.count({
      where: {
        gameId,
        status: 'COMPLETED'
      }
    });

    const completionRate = orderStats._count > 0 
      ? (completedOrders / orderStats._count) * 100 
      : 0;

    // 获取热门段位
    const popularRanks = await prisma.order.groupBy({
      by: ['currentRankId'],
      where: { gameId },
      _count: true,
      orderBy: {
        _count: {
          currentRankId: 'desc'
        }
      },
      take: 5
    });

    const popularRanksWithNames = await Promise.all(
      popularRanks.map(async (rank) => {
        if (!rank.currentRankId) {
          return {
            rankName: '未知段位',
            orderCount: rank._count
          };
        }
        const rankInfo = await prisma.gameRank.findUnique({
          where: { id: rank.currentRankId },
          select: { name: true }
        });
        return {
          rankName: rankInfo?.name || '未知段位',
          orderCount: rank._count
        };
      })
    );

    // 获取月度趋势（最近6个月）
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyOrders = await prisma.order.findMany({
      where: {
        gameId,
        createdAt: {
          gte: sixMonthsAgo
        }
      },
      select: {
        createdAt: true,
        price: true
      }
    });

    // 按月分组统计
    const monthlyTrend = monthlyOrders.reduce((acc, order) => {
      const month = order.createdAt.toISOString().slice(0, 7); // YYYY-MM
      if (!acc[month]) {
        acc[month] = { orderCount: 0, revenue: 0 };
      }
      acc[month].orderCount++;
      acc[month].revenue += order.price;
      return acc;
    }, {} as Record<string, { orderCount: number; revenue: number }>);

    const monthlyTrendArray = Object.entries(monthlyTrend).map(([month, data]) => ({
      month,
      orderCount: data.orderCount,
      revenue: data.revenue
    }));

    return {
      gameId,
      gameName: game.displayName,
      totalOrders: orderStats._count,
      totalRevenue: orderStats._sum.price || 0,
      averageOrderValue: orderStats._avg.price || 0,
      completionRate,
      popularRanks: popularRanksWithNames,
      monthlyTrend: monthlyTrendArray
    };
  }

  // 计算订单价格
  async calculateOrderPrice(
    gameId: string,
    currentRankId: string,
    targetRankId: string,
    priority: string = 'NORMAL'
  ): Promise<PriceCalculationResult> {
    // 获取段位信息
    const [currentRank, targetRank] = await Promise.all([
      prisma.gameRank.findUnique({ where: { id: currentRankId } }),
      prisma.gameRank.findUnique({ where: { id: targetRankId } })
    ]);

    if (!currentRank || !targetRank) {
      throw new NotFoundError('段位信息不存在');
    }

    if (currentRank.gameId !== gameId || targetRank.gameId !== gameId) {
      throw new ValidationError('段位与游戏不匹配');
    }

    const levelDifference = targetRank.level - currentRank.level;
    if (levelDifference <= 0) {
      throw new ValidationError('目标段位必须高于当前段位');
    }

    // 获取适用的价格规则
    const priceRules = await prisma.gamePriceRule.findMany({
      where: {
        gameId,
        isActive: true,
        OR: [
          { priority: null },
          { priority: priority as any }
        ],
        AND: [
          {
            OR: [
              { minLevel: null },
              { minLevel: { lte: currentRank.level } }
            ]
          },
          {
            OR: [
              { maxLevel: null },
              { maxLevel: { gte: targetRank.level } }
            ]
          }
        ]
      },
      orderBy: [
        { priority: 'desc' },
        { basePrice: 'desc' }
      ]
    });

    // 使用第一个匹配的规则
    const rule = priceRules[0];
    if (!rule) {
      throw new NotFoundError('未找到适用的价格规则');
    }

    // 计算价格
    const basePrice = rule.basePrice;
    const levelPrice = (rule.pricePerLevel || 0) * levelDifference;
    const difficultyMultiplier = (currentRank.difficultyMultiplier + targetRank.difficultyMultiplier) / 2;
    
    // 优先级倍数
    const priorityMultipliers = {
      LOW: 0.8,
      NORMAL: 1.0,
      HIGH: 1.3,
      URGENT: 1.6
    };
    const priorityMultiplier = priorityMultipliers[priority as keyof typeof priorityMultipliers] || 1.0;

    const finalPrice = Math.round(
      (basePrice + levelPrice) * difficultyMultiplier * priorityMultiplier * rule.multiplier
    );

    return {
      basePrice,
      levelDifference,
      difficultyMultiplier,
      priorityMultiplier,
      finalPrice,
      appliedRules: [rule.name]
    };
  }

  // 获取游戏热度分析
  async getGameHotness(): Promise<GameHotness[]> {
    const games = await prisma.game.findMany({
      where: { isActive: true },
      include: {
        _count: {
          select: {
            orders: true,
            employeeSkills: true
          }
        }
      }
    });

    const hotnessData = await Promise.all(
      games.map(async (game) => {
        // 获取收益统计
        const revenueStats = await prisma.order.aggregate({
          where: { gameId: game.id },
          _sum: { price: true }
        });

        // 获取平均完成时间
        const completedTasks = await prisma.task.findMany({
          where: {
            order: { gameId: game.id },
            status: 'COMPLETED',
            startTime: { not: null },
            endTime: { not: null }
          },
          select: {
            startTime: true,
            endTime: true
          }
        });

        const avgCompletionTime = completedTasks.length > 0
          ? completedTasks.reduce((sum, task) => {
              const duration = task.endTime!.getTime() - task.startTime!.getTime();
              return sum + duration;
            }, 0) / completedTasks.length / (1000 * 60 * 60) // 转换为小时
          : 0;

        // 计算热度评分
        const orderCount = game._count.orders;
        const revenue = revenueStats._sum.price || 0;
        const employeeCount = game._count.employeeSkills;

        // 热度评分算法：订单数量 * 0.4 + 收益/100 * 0.3 + 员工数量 * 0.3
        const hotnesScore = Math.round(
          orderCount * 0.4 + (revenue / 100) * 0.3 + employeeCount * 0.3
        );

        // 简单的趋势分析（基于最近30天vs之前30天的订单数量）
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const sixtyDaysAgo = new Date();
        sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

        const [recentOrders, previousOrders] = await Promise.all([
          prisma.order.count({
            where: {
              gameId: game.id,
              createdAt: { gte: thirtyDaysAgo }
            }
          }),
          prisma.order.count({
            where: {
              gameId: game.id,
              createdAt: { gte: sixtyDaysAgo, lt: thirtyDaysAgo }
            }
          })
        ]);

        let trend: 'rising' | 'stable' | 'declining' = 'stable';
        if (recentOrders > previousOrders * 1.1) {
          trend = 'rising';
        } else if (recentOrders < previousOrders * 0.9) {
          trend = 'declining';
        }

        return {
          gameId: game.id,
          gameName: game.displayName,
          orderCount,
          revenue,
          averageCompletionTime: Math.round(avgCompletionTime),
          employeeCount,
          hotnesScore,
          trend
        };
      })
    );

    // 按热度评分排序
    return hotnessData.sort((a, b) => b.hotnesScore - a.hotnesScore);
  }

  // 获取所有活跃游戏的简单列表（用于下拉选择）
  async getActiveGamesSimple() {
    return await prisma.game.findMany({
      where: { isActive: true },
      select: {
        id: true,
        name: true,
        displayName: true,
        icon: true
      },
      orderBy: { sortOrder: 'asc' }
    });
  }
}
