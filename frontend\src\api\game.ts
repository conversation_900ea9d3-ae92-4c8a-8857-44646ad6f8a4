import { request } from './http';
import type {
  Game,
  GameRank,
  GamePriceRule,
  EmployeeGameSkill,
  CreateGameRequest,
  UpdateGameRequest,
  CreateGameRankRequest,
  UpdateGameRankRequest,
  GameQuery,
  GameRankQuery,
  GameStatistics,
  GameHotness,
  PriceCalculationResult,
  SimpleGame,
  SimpleGameRank
} from '@/types/game';
import type { PaginatedResponse, ApiResponse } from '@/types';

// 游戏管理API
export const gameApi = {
  // 获取活跃游戏简单列表
  getActiveGames(): Promise<ApiResponse<SimpleGame[]>> {
    return request.get('/games/active');
  },

  // 获取游戏及其表单字段
  getGameWithFormFields(id: string): Promise<ApiResponse<Game>> {
    return request.get(`/games/${id}/form-fields`);
  },

  // 获取游戏列表
  getGames(params?: GameQuery): Promise<PaginatedResponse<Game>> {
    return request.get('/games', { params });
  },

  // 获取游戏详情
  getGameById(id: string): Promise<Game> {
    return request.get(`/games/${id}`);
  },

  // 创建游戏
  createGame(data: CreateGameRequest): Promise<Game> {
    return request.post('/games', data);
  },

  // 更新游戏
  updateGame(id: string, data: UpdateGameRequest): Promise<Game> {
    return request.put(`/games/${id}`, data);
  },

  // 删除游戏
  deleteGame(id: string): Promise<{ message: string }> {
    return request.delete(`/games/${id}`);
  },

  // 获取游戏统计信息
  getGameStatistics(id: string): Promise<GameStatistics> {
    return request.get(`/games/${id}/statistics`);
  },

  // 获取游戏热度分析
  getGameHotness(): Promise<GameHotness[]> {
    return request.get('/games/hotness');
  },

  // 计算订单价格
  calculateOrderPrice(data: {
    gameId: string;
    currentRankId: string;
    targetRankId: string;
    priority?: string;
  }): Promise<PriceCalculationResult> {
    return request.post('/games/calculate-price', data);
  }
};

// 游戏段位管理API
export const gameRankApi = {
  // 获取指定游戏的段位列表
  getGameRanksByGameId(gameId: string): Promise<SimpleGameRank[]> {
    return request.get(`/games/${gameId}/ranks`);
  },

  // 获取游戏段位列表
  getGameRanks(params?: GameRankQuery): Promise<PaginatedResponse<GameRank>> {
    return request.get('/games/ranks', { params });
  },

  // 获取段位详情
  getGameRankById(id: string): Promise<GameRank> {
    return request.get(`/games/ranks/${id}`);
  },

  // 创建游戏段位
  createGameRank(data: CreateGameRankRequest): Promise<GameRank> {
    return request.post('/games/ranks', data);
  },

  // 更新段位
  updateGameRank(id: string, data: UpdateGameRankRequest): Promise<GameRank> {
    return request.put(`/games/ranks/${id}`, data);
  },

  // 删除段位
  deleteGameRank(id: string): Promise<{ message: string }> {
    return request.delete(`/games/ranks/${id}`);
  },

  // 批量创建段位
  batchCreateGameRanks(gameId: string, ranks: Omit<CreateGameRankRequest, 'gameId'>[]): Promise<{ message: string; count: number }> {
    return request.post(`/games/${gameId}/ranks/batch`, { ranks });
  }
};

// 游戏价格规则API
export const gamePriceRuleApi = {
  // 获取价格规则列表
  getPriceRules(params?: { gameId?: string; isActive?: boolean }): Promise<PaginatedResponse<GamePriceRule>> {
    return request.get('/games/price-rules', { params });
  },

  // 获取价格规则详情
  getPriceRuleById(id: string): Promise<GamePriceRule> {
    return request.get(`/games/price-rules/${id}`);
  },

  // 创建价格规则
  createPriceRule(data: any): Promise<GamePriceRule> {
    return request.post('/games/price-rules', data);
  },

  // 更新价格规则
  updatePriceRule(id: string, data: any): Promise<GamePriceRule> {
    return request.put(`/games/price-rules/${id}`, data);
  },

  // 删除价格规则
  deletePriceRule(id: string): Promise<{ message: string }> {
    return request.delete(`/games/price-rules/${id}`);
  }
};

// 员工游戏技能API
export const employeeGameSkillApi = {
  // 获取员工游戏技能列表
  getEmployeeGameSkills(params?: {
    userId?: string;
    gameId?: string;
    skillLevel?: string;
    isActive?: boolean;
  }): Promise<PaginatedResponse<EmployeeGameSkill>> {
    return request.get('/games/employee-skills', { params });
  },

  // 获取员工游戏技能详情
  getEmployeeGameSkillById(id: string): Promise<EmployeeGameSkill> {
    return request.get(`/games/employee-skills/${id}`);
  },

  // 创建员工游戏技能
  createEmployeeGameSkill(data: any): Promise<EmployeeGameSkill> {
    return request.post('/games/employee-skills', data);
  },

  // 更新员工游戏技能
  updateEmployeeGameSkill(id: string, data: any): Promise<EmployeeGameSkill> {
    return request.put(`/games/employee-skills/${id}`, data);
  },

  // 删除员工游戏技能
  deleteEmployeeGameSkill(id: string): Promise<{ message: string }> {
    return request.delete(`/games/employee-skills/${id}`);
  },

  // 认证员工技能
  certifyEmployeeSkill(id: string): Promise<EmployeeGameSkill> {
    return request.post(`/games/employee-skills/${id}/certify`);
  }
};

// 游戏相关的统计和分析API
export const gameAnalyticsApi = {
  // 获取游戏订单分布统计
  getGameOrderDistribution(): Promise<{
    gameId: string;
    gameName: string;
    orderCount: number;
    percentage: number;
  }[]> {
    return request.get('/games/analytics/order-distribution');
  },

  // 获取游戏收益统计
  getGameRevenueStats(params?: {
    startDate?: string;
    endDate?: string;
    gameId?: string;
  }): Promise<{
    gameId: string;
    gameName: string;
    revenue: number;
    orderCount: number;
    averageOrderValue: number;
  }[]> {
    return request.get('/games/analytics/revenue', { params });
  },

  // 获取游戏趋势分析
  getGameTrends(params?: {
    period?: 'week' | 'month' | 'quarter';
    gameId?: string;
  }): Promise<{
    date: string;
    gameStats: {
      gameId: string;
      gameName: string;
      orderCount: number;
      revenue: number;
    }[];
  }[]> {
    return request.get('/games/analytics/trends', { params });
  },

  // 获取员工游戏技能分布
  getEmployeeSkillDistribution(gameId?: string): Promise<{
    skillLevel: string;
    count: number;
    percentage: number;
  }[]> {
    return request.get('/games/analytics/skill-distribution', {
      params: { gameId }
    });
  }
};

// 便捷导出函数
export const getActiveGames = async (): Promise<Game[]> => {
  const response = await gameApi.getActiveGames();
  return response.data;
};

export const getGameWithFormFields = async (id: string): Promise<Game> => {
  const response = await gameApi.getGameWithFormFields(id);
  return response.data;
};

export const getGames = async (params?: GameQuery): Promise<{
  items: Game[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}> => {
  const response = await gameApi.getGames(params);
  return response.data;
};

export const updateGame = async (id: string, data: UpdateGameRequest): Promise<Game> => {
  const response = await gameApi.updateGame(id, data);
  return response.data;
};

export const deleteGame = async (id: string): Promise<void> => {
  await gameApi.deleteGame(id);
};

// 导出所有API
export default {
  game: gameApi,
  gameRank: gameRankApi,
  gamePriceRule: gamePriceRuleApi,
  employeeGameSkill: employeeGameSkillApi,
  gameAnalytics: gameAnalyticsApi
};
